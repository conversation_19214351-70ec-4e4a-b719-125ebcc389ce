package dev.pigmomo.yhkit2025.ui.screens

import android.annotation.SuppressLint
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Done
import androidx.compose.material.icons.filled.Place
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.FloatingActionButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.api.model.user.AddressItem
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.ui.components.SearchBar
import dev.pigmomo.yhkit2025.ui.dialog.OderDeliveryChangeDialog
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.dialog.AccountDialog
import dev.pigmomo.yhkit2025.ui.dialog.DeletedOrderRecordsDialog
import dev.pigmomo.yhkit2025.ui.dialog.ActivityRuleDialog
import dev.pigmomo.yhkit2025.ui.dialog.AddAddressDialog
import dev.pigmomo.yhkit2025.ui.dialog.BindCardDialog
import dev.pigmomo.yhkit2025.ui.dialog.BoostCouponConfigDialog
import dev.pigmomo.yhkit2025.ui.dialog.BoostCouponDetailDialog
import dev.pigmomo.yhkit2025.ui.dialog.CardRecordOrderListDialog
import dev.pigmomo.yhkit2025.ui.dialog.CartDialog
import dev.pigmomo.yhkit2025.ui.dialog.DeliveryPhotosDialog
import dev.pigmomo.yhkit2025.ui.dialog.HandLuckDialog
import dev.pigmomo.yhkit2025.ui.dialog.KindCouponDialog
import dev.pigmomo.yhkit2025.ui.dialog.NewPersonCouponDialog
import dev.pigmomo.yhkit2025.ui.dialog.OrderDetailDialog
import dev.pigmomo.yhkit2025.ui.dialog.OrderPlaceDialog
import dev.pigmomo.yhkit2025.ui.dialog.OrderSearchDialog
import dev.pigmomo.yhkit2025.ui.dialog.ProductSearchDialog
import dev.pigmomo.yhkit2025.ui.dialog.CancelSendCardDialog
import dev.pigmomo.yhkit2025.ui.dialog.CouponDetailDialog
import dev.pigmomo.yhkit2025.ui.dialog.SendCardDialog
import dev.pigmomo.yhkit2025.ui.dialog.InvoiceCanApplyOrderListDialog
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.viewmodel.OrderViewModel
import java.text.SimpleDateFormat
import java.util.Date
import java.util.UUID
import dev.pigmomo.yhkit2025.ui.dialog.AfterSalesListDialog
import dev.pigmomo.yhkit2025.ui.dialog.AutoOrderPlaceData
import dev.pigmomo.yhkit2025.ui.dialog.BindInvitationCodeDialog
import dev.pigmomo.yhkit2025.ui.dialog.HandLuckRewardListDialog
import dev.pigmomo.yhkit2025.ui.dialog.JoinPointTeamDialog
import dev.pigmomo.yhkit2025.ui.dialog.OrderAutoPlaceDialog
import dev.pigmomo.yhkit2025.ui.dialog.MultiThreadDialog
import dev.pigmomo.yhkit2025.ui.dialog.OperationIntervalDialog
import dev.pigmomo.yhkit2025.utils.AutoOrderPlaceUtils.autoOrderPlace
import dev.pigmomo.yhkit2025.ui.dialog.OperationDialog
import dev.pigmomo.yhkit2025.ui.dialog.OrderTokenDetailDialog
import dev.pigmomo.yhkit2025.utils.ProgressManager
import dev.pigmomo.yhkit2025.utils.BatchOperationUtils
import dev.pigmomo.yhkit2025.utils.FailedTokenIndexRecordUtils
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.util.Locale
import dev.pigmomo.yhkit2025.ui.dialog.OrderExportDialog
import dev.pigmomo.yhkit2025.utils.OrderDataCacheManager
import android.widget.Toast
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.ui.graphics.graphicsLayer
import dev.pigmomo.yhkit2025.service.BackgroundService
import dev.pigmomo.yhkit2025.ui.dialog.CouponGrabbingConfigDialog
import dev.pigmomo.yhkit2025.ui.dialog.ExportOrderAccountDialog
import dev.pigmomo.yhkit2025.utils.CouponGrabbingUtils
import dev.pigmomo.yhkit2025.utils.OperationProgressUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.ui.Alignment
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.unit.Dp
import dev.pigmomo.yhkit2025.utils.DatabaseMaintenanceUtils

@SuppressLint("FrequentlyChangedStateReadInComposition", "DefaultLocale")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OrderScreen(viewModel: OrderViewModel) {
    val context = LocalContext.current

    val orderTokens by viewModel.orderTokens.collectAsState()
    val clickedTokenUid by viewModel.clickedTokenUid
    val longClickedTokenUid by viewModel.longClickedTokenUid
    val currentOperationOrderTokenIndex by viewModel.currentOperationOrderTokenIndex
    val isTokenDetailDialogVisible by viewModel.isTokenDetailDialogVisible
    val isAccountExporting by viewModel.accountExporting
    val isAccountImporting by viewModel.accountImporting
    val serviceType by viewModel.serviceType
    val enableProxy by viewModel.enableProxy
    val selectedProxy by viewModel.selectedProxy
    val autoOrderPlace by viewModel.autoOrderPlace
    val isLoadingTokenInfo by viewModel.isLoadingTokenInfo
    val showSearchFrame by viewModel.showSearchFrame
    val showOrderTokensOperateDialog by viewModel.showOrderTokensOperateDialog
    val showActivitiesOperateDialog by viewModel.showActivitiesOperateDialog
    val searchKeyword by viewModel.searchKeyword
    val isOrderTokensOperating by viewModel.isOrderTokensOperating
    val isActivitiesOperating by viewModel.isActivitiesOperating
    val isExportAccountDialogVisible by viewModel.isExportAccountDialogVisible

    // 删除订单记录相关状态
    val showDeletedOrderRecordsDialog by viewModel.showDeletedOrderRecordsDialog
    val deletedOrderRecords by viewModel.deletedOrderRecords.collectAsState()

    val lazyListState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()

    // 检查当前选中的账号是否在屏幕可见范围内
    val isClickedTokenVisible = remember(
        clickedTokenUid,
        lazyListState.firstVisibleItemIndex,
        lazyListState.layoutInfo.visibleItemsInfo
    ) {
        if (clickedTokenUid.isEmpty()) {
            true // 如果没有选中的账号，不需要显示按钮
        } else {
            val clickedTokenIndex = orderTokens.indexOfFirst { it.uid == clickedTokenUid }
            if (clickedTokenIndex == -1) {
                true // 如果找不到选中的账号，不需要显示按钮
            } else {
                // 检查选中的账号是否在当前可见项中
                lazyListState.layoutInfo.visibleItemsInfo.any { it.index == clickedTokenIndex }
            }
        }
    }

    // 检查当前操作的账号是否在屏幕可见范围内
    val isCurrentOperationOrderTokenVisible = remember(
        currentOperationOrderTokenIndex,
        lazyListState.firstVisibleItemIndex,
        lazyListState.layoutInfo.visibleItemsInfo
    ) {
        if (currentOperationOrderTokenIndex == -1 || currentOperationOrderTokenIndex >= orderTokens.size) {
            true // 如果没有操作的账号，不需要显示按钮
        } else {
            // 检查选中的账号是否在当前可见项中
            lazyListState.layoutInfo.visibleItemsInfo.any { it.index == currentOperationOrderTokenIndex }
        }
    }

    // 订阅进度记录流
    val progressRecords by ProgressManager.progressRecordsFlow.collectAsState()
    LaunchedEffect(progressRecords, orderTokens) {
        if (progressRecords.isEmpty()) {
            // 如果进度记录为空，同步所有账号的进度
            ProgressManager.syncLatestRecordFromDbByOrderTokens(context, orderTokens)
        } else {
            // 如果进度记录不为空，只同步新导入账号的进度
            ProgressManager.syncNewAccountsProgress(context, orderTokens)
        }
    }

    // 定期检查数据库健康状态（每30分钟检查一次）
    LaunchedEffect(Unit) {
        while (isActive) {
            try {
                delay(30 * 60 * 1000L) // 30分钟

                // 再次检查是否仍然活跃，避免在延迟期间被取消后继续执行
                if (!isActive) break

                val dbSize = DatabaseMaintenanceUtils.getDatabaseSizeMB(context)
                if (dbSize > 50) { // 如果数据库超过50MB
                    Log.w(
                        "OrderScreen",
                        "Database size is large: ${dbSize}MB, consider maintenance"
                    )
                    DatabaseMaintenanceUtils.performAutoMaintenance(context)
                }
            } catch (e: kotlinx.coroutines.CancellationException) {
                // 协程被取消是正常行为，不需要记录错误
                Log.d("OrderScreen", "Database health check cancelled")
                break
            } catch (e: Exception) {
                Log.e("OrderScreen", "Database health check failed", e)
                // 发生其他异常时退出循环，避免无限重试
                break
            }
        }
    }

    // 监听需要滚动的令牌变化，滚动到对应位置
    val scrollToTokenUid by viewModel.scrollToTokenUid
    LaunchedEffect(scrollToTokenUid) {
        if (scrollToTokenUid.isNotEmpty()) {
            val index = orderTokens.indexOfFirst { it.uid == scrollToTokenUid }
            if (index >= 0) {
                coroutineScope.launch {
                    lazyListState.animateScrollToItem(index)
                    // 滚动完成后重置状态，避免重复滚动
                    viewModel.resetScrollToToken()
                }
            } else {
                // 如果找不到对应的令牌，也需要重置状态
                viewModel.resetScrollToToken()
            }
        }
    }

    // 地址删除相关状态
    val addressToDelete by viewModel.addressToDelete

    // 展示文本
    val accountDisplayText by viewModel.accountDisplayText
    val addressDisplayText by viewModel.addressDisplayText
    val cartDisplayText by viewModel.cartDisplayText
    val shopDisplayText by viewModel.shopDisplayText

    // 账号列表展示文本
    val displayStr = remember(orderTokens) {
        "账号列表(${orderTokens.size})"
    }

    // 展示相关参数
    val orderListInfoValue by viewModel.orderListInfo
    val cardBalanceValue by viewModel.cardBalance
    val creditData by viewModel.creditData
    val accountCardTopStartText = remember(orderListInfoValue, cardBalanceValue, creditData) {
        (if (cardBalanceValue != "0" && cardBalanceValue != "0.00") "余额$cardBalanceValue " else "") +
                (if (creditData != null && (creditData?.credit
                        ?: 0) != 0
                ) "积分${creditData?.credit} " else "") +
                orderListInfoValue.ifEmpty { "" }
    }

    val needLogin by viewModel.needLogin
    val accountType by viewModel.accountType
    val checkCardBuyStr by viewModel.checkCardBuyStr
    val checkActivityStr by viewModel.checkActivityStr
    val realProxyIp by viewModel.realProxyIp
    val phoneLocation by viewModel.phoneLocation
    val accountCardBottomEndText =
        remember(
            needLogin,
            accountType,
            checkCardBuyStr,
            checkActivityStr,
            realProxyIp,
            phoneLocation
        ) {
            if (needLogin) "未登录" else "${accountType}${if (checkCardBuyStr.isNotEmpty() && checkActivityStr.isNotEmpty()) " $checkCardBuyStr/$checkActivityStr" else if (checkCardBuyStr.isNotEmpty()) " $checkCardBuyStr" else if (checkActivityStr.isNotEmpty()) " $checkActivityStr" else ""}${if (phoneLocation.isNotEmpty()) " $phoneLocation" else ""}${if (realProxyIp.isNotEmpty()) " IP:$realProxyIp" else ""}"
        }

    // 账号导出文件选择器
    val accountExportFileLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.CreateDocument("text/plain")
    ) { uri ->
        uri?.let {
            viewModel.exportAccountsToUri(it)
        }
    }

    // 账号导入文件选择器
    val accountImportFileLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.OpenDocument()
    ) { uri ->
        uri?.let {
            viewModel.importAccountsFromUri(it)
        }
    }

    val currentSelectedAccount by viewModel.currentSelectedAccount

    val longSelectedToken = orderTokens.find { it.uid == longClickedTokenUid }

    // 用于搜索结果的状态
    var filteredTokens by remember { mutableStateOf<List<OrderTokenEntity>>(emptyList()) }
    var currentSearchResultIndex by remember { mutableStateOf(-1) }

    // 监听应用恢复时检查剪贴板
    LaunchedEffect(viewModel.checkClipboardOnResume) {
        if (viewModel.checkClipboardOnResume) {
            viewModel.handleClipboardTextChange()
            viewModel.checkClipboardOnResume = false
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row {
                        Text(
                            text = "YH BUY",
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    actionIconContentColor = Color.White
                ),
                actions = {
                    // 导入账户、删除订单按钮
                    IconButton(
                        onClick = {
                            // txt, csv
                            accountImportFileLauncher.launch(
                                arrayOf(
                                    "text/plain",
                                    "text/comma-separated-values"
                                )
                            )
                        },
                        enabled = !isLoadingTokenInfo && !isAccountExporting && !isAccountImporting && !isOrderTokensOperating && !isActivitiesOperating
                    ) {
                        if (isAccountImporting) {
                            CircularProgressIndicator(
                                modifier = Modifier.padding(8.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                        } else {
                            Icon(
                                painter = painterResource(id = R.drawable.baseline_move_to_inbox_24),
                                contentDescription = null,
                                tint = if (isLoadingTokenInfo || isAccountExporting || isOrderTokensOperating || isActivitiesOperating)
                                    MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.5f)
                                else MaterialTheme.colorScheme.onPrimary
                            )
                        }
                    }

                    // 导出账户按钮
                    IconButton(
                        onClick = {
                            if (orderTokens.isNotEmpty()) {
                                viewModel.setExportAccountDialogVisible(true)
                            }
                        },
                        enabled = !isLoadingTokenInfo && !isAccountExporting && !isAccountImporting && orderTokens.isNotEmpty() && !isOrderTokensOperating && !isActivitiesOperating
                    ) {
                        if (isAccountExporting) {
                            CircularProgressIndicator(
                                modifier = Modifier.padding(8.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                        } else {
                            Icon(
                                painter = painterResource(id = R.drawable.baseline_outbound_24),
                                contentDescription = null,
                                tint = if (isLoadingTokenInfo || orderTokens.isEmpty() || isAccountImporting || isOrderTokensOperating || isActivitiesOperating)
                                    MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.5f)
                                else MaterialTheme.colorScheme.onPrimary
                            )
                        }
                    }

                    // 服务类型按钮
                    IconButton(
                        onClick = {
                            if (serviceType == "app") {
                                viewModel.setServiceType("mini")
                            } else {
                                viewModel.setServiceType("app")
                            }
                        },
                        enabled = !isLoadingTokenInfo && !isAccountExporting && !isAccountImporting && !isOrderTokensOperating && !isActivitiesOperating
                    ) {
                        BadgedBox(
                            badge = {
                                Text(
                                    text = serviceType,
                                    color = if (serviceType == "app" || isLoadingTokenInfo || isAccountExporting || isOrderTokensOperating || isActivitiesOperating) MaterialTheme.colorScheme.onPrimary.copy(
                                        alpha = 0.5f
                                    ) else MaterialTheme.colorScheme.onPrimary,
                                    fontSize = 7.sp
                                )
                            }
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.outline_microbiology_24),
                                contentDescription = null,
                                tint = if (serviceType == "app" || isLoadingTokenInfo || isAccountExporting || isOrderTokensOperating || isActivitiesOperating) MaterialTheme.colorScheme.onPrimary.copy(
                                    alpha = 0.5f
                                ) else MaterialTheme.colorScheme.onPrimary
                            )
                        }
                    }

                    // 代理设置按钮
                    IconButton(
                        onClick = {
                            if (!enableProxy) {
                                viewModel.setShowProxySelectDialog(true)
                            } else {
                                viewModel.setEnableProxy(false)
                            }
                        },
                        enabled = !isLoadingTokenInfo && !isAccountExporting && !isAccountImporting && !isOrderTokensOperating && !isActivitiesOperating
                    ) {
                        val selectedProxyStr = when (selectedProxy) {
                            "pinzan" -> "品赞"
                            "shanchen" -> "闪臣"
                            "ipcola_global" -> "IPA"
                            "oxylabs_global" -> "OXY"
                            else -> ""
                        }
                        BadgedBox(
                            badge = {
                                Text(
                                    text = selectedProxyStr,
                                    fontSize = 7.sp,
                                    color = if (isLoadingTokenInfo || isAccountExporting || isOrderTokensOperating || isActivitiesOperating) MaterialTheme.colorScheme.onPrimary.copy(
                                        alpha = 0.5f
                                    ) else MaterialTheme.colorScheme.onPrimary
                                )
                            }
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.baseline_vpn_lock_24),
                                contentDescription = null,
                                tint = if (isLoadingTokenInfo || isAccountExporting || !enableProxy || isOrderTokensOperating || isActivitiesOperating) MaterialTheme.colorScheme.onPrimary.copy(
                                    alpha = 0.5f
                                ) else MaterialTheme.colorScheme.onPrimary
                            )
                        }
                    }

                    // 自动下单按钮
                    IconButton(
                        onClick = {
                            if (autoOrderPlace) {
                                viewModel.setAutoOrderPlace(false)
                            } else {
                                viewModel.setShowOrderAutoPlaceDialog(true)
                            }
                        },
                        enabled = !isLoadingTokenInfo && !isAccountExporting && !isAccountImporting && !isOrderTokensOperating && !isActivitiesOperating
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.baseline_auto_fix_high_24),
                            contentDescription = null,
                            tint = if (!autoOrderPlace || isLoadingTokenInfo || isAccountExporting || isAccountImporting || isOrderTokensOperating || isActivitiesOperating) MaterialTheme.colorScheme.onPrimary.copy(
                                alpha = 0.5f
                            ) else MaterialTheme.colorScheme.onPrimary
                        )
                    }
                }
            )
        },
        floatingActionButton = {
            // 使用AnimatedVisibility包装FloatingActionButton
            AnimatedVisibility(
                visible = (!isClickedTokenVisible && clickedTokenUid.isNotEmpty()) || (!isCurrentOperationOrderTokenVisible && currentOperationOrderTokenIndex != -1),
                enter = fadeIn(animationSpec = tween(300)) + slideInVertically(
                    initialOffsetY = { it * 2 },
                    animationSpec = tween(300)
                ),
                exit = fadeOut(animationSpec = tween(300)) + slideOutVertically(
                    targetOffsetY = { it * 2 },
                    animationSpec = tween(300)
                )
            ) {
                FloatingActionButton(
                    onClick = {
                        // 优先定位到当前操作的账号（当isOrderTokensOperating或isActivitiesOperating为true时）
                        if ((isOrderTokensOperating || isActivitiesOperating) && !isCurrentOperationOrderTokenVisible && currentOperationOrderTokenIndex != -1) {
                            coroutineScope.launch {
                                lazyListState.animateScrollToItem(currentOperationOrderTokenIndex - 1)
                            }
                            return@FloatingActionButton
                        }

                        // 其次定位到点击选中的账号
                        val clickedTokenIndex =
                            orderTokens.indexOfFirst { it.uid == clickedTokenUid }
                        if (!isClickedTokenVisible && clickedTokenIndex != -1) {
                            coroutineScope.launch {
                                lazyListState.animateScrollToItem(clickedTokenIndex)
                            }
                            return@FloatingActionButton
                        }

                        // 最后定位到当前操作的账号（当不在批量操作时）
                        if (!isCurrentOperationOrderTokenVisible && currentOperationOrderTokenIndex != -1) {
                            coroutineScope.launch {
                                lazyListState.animateScrollToItem(currentOperationOrderTokenIndex - 1)
                            }
                            return@FloatingActionButton
                        }
                    },
                    containerColor = MaterialTheme.colorScheme.primary,
                    contentColor = Color.White,
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Icon(
                        imageVector = Icons.Filled.Place,
                        contentDescription = "定位到当前操作账号"
                    )
                }
            }
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .padding(paddingValues)
                .fillMaxSize()
                .imePadding()
        ) {
            Column {
                // 账号部分
                SectionLabelText(
                    label = "账号: ",
                    modifier = Modifier.padding(start = 15.dp, top = 10.dp)
                )
                AccountCard(
                    onClick = {
                        if (currentSelectedAccount != null && viewModel.selectedAddressItem.value != null) {
                            viewModel.setShowAccountDialog(true)
                        }
                    },
                    onLongClick = { },
                    displayText = accountDisplayText,
                    topStartText = accountCardTopStartText,
                    bottomEndText = accountCardBottomEndText
                )

                // 地址部分
                SectionLabelText(
                    label = "地址: ",
                    modifier = Modifier.padding(start = 15.dp, end = 12.dp)
                )
                AddressCard(
                    displayText = addressDisplayText,
                    viewModel = viewModel,
                    selectedAddressItem = viewModel.selectedAddressItem.value,
                    showAddressMenu = viewModel.showAddressMenu.value,
                    addressList = viewModel.addressList.value,
                    onCardClick = {
                        if (currentSelectedAccount != null && viewModel.addressList.value.isNotEmpty()) {
                            viewModel.setShowAddressMenu(
                                !viewModel.showAddressMenu.value
                            )
                        }
                    },
                    onDismissMenu = { viewModel.setShowAddressMenu(false) },
                    onSelectAddress = { addressItem ->
                        viewModel.switchAddress(addressItem)
                        viewModel.setShowAddressMenu(false)
                    },
                    onDeleteAddress = { addressItem ->
                        // 显示删除地址确认对话框
                        viewModel.setShowDeleteAddressConfirmDialog(true, addressItem)
                    },
                    onSaveAddress = { addressItem ->
                        // 调用保存地址配置方法
                        // 构建正确格式的地址和位置对象
                        val addressStr =
                            """{"area":"${addressItem.address.area}","city":"${addressItem.address.city}","cityid":"${addressItem.address.cityid}","detail":"${addressItem.address.detail}","district":"${viewModel.selectedDistrict.value}","_uuid":"${UUID.randomUUID()}"}"""
                        val locationStr =
                            """{"lat":"${addressItem.location?.lat}","lng":"${addressItem.location?.lng}","_uuid":"${UUID.randomUUID()}"}"""

                        // 从viewModel中获取的值
                        val detailsRegex = viewModel.randomDetailsRegex.value
                        val comment = viewModel.orderComment.value
                        val shopItem = viewModel.currentShopItem.value
                        viewModel.saveAddressConfig(
                            addressStr,
                            locationStr,
                            detailsRegex,
                            comment,
                            shopItem
                        )
                        Toast.makeText(context, "地址配置已保存", Toast.LENGTH_SHORT).show()
                    },
                    actions = {
                        ActionButton(
                            icon = Icons.Filled.Refresh,
                            onClick = { viewModel.refreshAddressRelatedData() },
                            enabled = currentSelectedAccount != null && !viewModel.fastenAddress.value
                        )
                        ActionButton(
                            icon = Icons.Filled.Add,
                            onClick = {
                                viewModel.setShowAddAddressDialog(true)
                            },
                            enabled = currentSelectedAccount != null
                        )
                        ActionButton(
                            icon = Icons.Filled.Delete,
                            onClick = {
                                // 显示清空地址确认对话框
                                viewModel.clearAddressConfirmDialog(true)
                            },
                            enabled = viewModel.addressList.value.isNotEmpty() && !viewModel.fastenAddress.value
                        )
                    }
                )

                // 购物车部分
                SectionLabelText(label = "购物车: ", modifier = Modifier.padding(start = 15.dp))
                CartCard(
                    displayText = cartDisplayText,
                    viewModel = viewModel,
                    onCardClick = {
                        if (viewModel.cartList.value.isNotEmpty()) viewModel.setShowCartDialog(
                            true
                        )
                    },
                    actions = {
                        ActionButton(
                            icon = Icons.Filled.Refresh,
                            onClick = { viewModel.refreshCart() },
                            enabled = viewModel.selectedAddressItem.value != null && !viewModel.fastenAddress.value
                        )
                        ActionButton(
                            icon = Icons.Filled.Add,
                            onClick = { viewModel.setShowAddProductDialog(true) },
                            enabled = viewModel.selectedAddressItem.value != null && !viewModel.fastenAddress.value
                        )
                        ActionButton(
                            icon = Icons.Filled.Delete,
                            onClick = {
                                // 显示清空购物车确认对话框
                                viewModel.setShowClearCartConfirmDialog(true)
                            },
                            enabled = viewModel.cartList.value.isNotEmpty() && !viewModel.fastenAddress.value
                        )
                        ActionButton(
                            icon = Icons.Filled.Done,
                            onClick = { viewModel.orderPlace() },
                            enabled = viewModel.hasSelectedCartItems.value && !viewModel.fastenAddress.value
                        )
                    },
                    modifier = Modifier.padding(end = 4.dp)
                )

                if (orderTokens.isNotEmpty()) {
                    AccountListHeader(
                        title = displayStr,
                        onClear = {
                            // 显示清空账号确认对话框
                            viewModel.setShowClearAllTokensConfirmDialog(true)
                        },
                        onSearch = {
                            // 切换搜索框显示状态
                            val newSearchFrameState = !showSearchFrame
                            viewModel.setShowSearchFrame(newSearchFrameState)
                            // 如果关闭搜索框，清空搜索关键词
                            if (!newSearchFrameState) {
                                viewModel.setSearchKeyword("")
                                filteredTokens = emptyList()
                                currentSearchResultIndex = -1
                            }
                        },
                        onTokensOperation = {
                            if (!viewModel.isOrderTokensOperating.value) viewModel.setShowOrderTokensOperateDialog(
                                !showOrderTokensOperateDialog
                            ) else {
                                viewModel.setIsOrderTokensOperating(false)
                            }
                        },
                        onActivitiesOperation = {
                            if (!viewModel.isActivitiesOperating.value) viewModel.setShowActivitiesOperateDialog(
                                !showActivitiesOperateDialog
                            ) else {
                                viewModel.setIsActivitiesOperating(false)
                            }
                        },
                        onShowNewPersonCouponShopParamsFastSetDialog = {
                            viewModel.setShowNewPersonCouponShopParamsFastSetDialog(
                                true
                            )
                        },
                        onShowHelpCouponConfigDialog = {
                            viewModel.setShowBoostCouponConfigDialog(true)
                        },
                        showSearchFrame = showSearchFrame,
                        isOrderTokensOperating = viewModel.isOrderTokensOperating.value,
                        isActivitiesOperating = viewModel.isActivitiesOperating.value,
                        isCouponGrabbing = viewModel.isCouponGrabbing.value,
                        onShowCouponGrabbingConfigDialog = {
                            if (!viewModel.isCouponGrabbing.value) {
                                viewModel.setShowCouponGrabbingConfigDialog(
                                    true
                                )
                            } else {
                                viewModel.setIsCouponGrabbing(false)
                            }
                        }
                    )
                }

                // 搜索框
                if (showSearchFrame) {
                    SearchBar(
                        searchKeyword = searchKeyword,
                        onValueChange = { viewModel.setSearchKeyword(it) },
                        orderTokens = orderTokens,
                        lazyListState = lazyListState,
                        onFilteredTokensChanged = { filteredTokens = it },
                        onCurrentIndexChanged = { currentSearchResultIndex = it },
                        progressRecords = progressRecords
                    )
                }

                Box(modifier = Modifier.weight(1f)) {
                    LazyColumn(
                        state = lazyListState,
                        modifier = Modifier.fillMaxSize()
                    ) {
                        items(
                            items = orderTokens,
                            key = { it.uid }
                        ) { orderToken ->
                            // 获取当前项在列表中的索引
                            val index = orderTokens.indexOf(orderToken) + 1

                            OrderTokenItem(
                                orderToken = orderToken,
                                isClicked = clickedTokenUid == orderToken.uid,
                                isLongClicked = longClickedTokenUid == orderToken.uid,
                                isCurrentOperationOrderToken = currentOperationOrderTokenIndex == index,
                                onClick = {
                                    viewModel.loadOrderTokenInfo(orderToken)
                                },
                                onLongClick = {
                                    viewModel.setLongClickedTokenUid(orderToken.uid)
                                    viewModel.setTokenDetailDialogVisible(true)
                                },
                                onDoubleClick = {
                                    viewModel.loadOrderTokenInfo(orderToken, false)
                                },
                                isSearchResult = filteredTokens.isNotEmpty() &&
                                        currentSearchResultIndex >= 0 &&
                                        currentSearchResultIndex < filteredTokens.size &&
                                        filteredTokens[currentSearchResultIndex].uid == orderToken.uid,
                                progressRecord = progressRecords[orderToken.uid] ?: "",
                                index = index // 传递序号
                            )
                        }
                    }
                    FastScrollBar(
                        state = lazyListState,
                        modifier = Modifier
                            .fillMaxHeight()
                            .padding(vertical = 4.dp)
                            .align(Alignment.CenterEnd)
                    )
                }
            }

            Column(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(16.dp)
            ) {
                // 抢券倒计时
                if (viewModel.isCouponGrabbing.value) {
                    CouponGrabbingIndicator(remainingTime = viewModel.couponGrabbingRemainingTime.value)
                }

                // 自动状态指示器
                if (OperationProgressUtils.isOperationInProgress()) {
                    OperationProgressIndicator()
                }
            }
        }
    }

    // 显示Token详情对话框
    if (isTokenDetailDialogVisible && longSelectedToken != null) {
        OrderTokenDetailDialog(
            token = longSelectedToken,
            viewModel = viewModel,
            onDismiss = { viewModel.setTokenDetailDialogVisible(false) }
        )
    }

    // 显示购物车对话框
    if (viewModel.showCartDialog.value) {
        CartDialog(
            cartItems = viewModel.cartList.value,
            currentCartItem = viewModel.currentCartItem.value,
            onDismiss = { viewModel.setShowCartDialog(false) },
            onItemClick = { product ->
                viewModel.onCartItemClick(product)
            },
            onAddItem = { product ->
                viewModel.addCartItemQuantity(product)
            },
            onReduceItem = { product ->
                viewModel.reduceCartItemQuantity(product)
            },
            onDeleteItem = { product ->
                // 显示删除购物车商品确认对话框
                viewModel.setShowDeleteCartItemConfirmDialog(true, product)
            },
            onItemLongClick = { product ->
                // TODO: 显示商品详情
            },
            onSaveProductConfig = { configString ->
                viewModel.saveProductConfig(configString)
            },
            onCartItemSelect = { cartItem ->
                viewModel.setCurrentCartItem(cartItem)
            },
            onProductSearch = {
                viewModel.setShowProductSearchDialog(true)
            }
        )
    }

    // 显示商品搜索对话框
    if (viewModel.showProductSearchDialog.value) {
        ProductSearchDialog(
            onDismiss = { viewModel.setShowProductSearchDialog(false) },
            onProductClick = { searchResult ->
                // 处理商品点击事件，可以跳转到商品详情或添加到购物车
                searchResult.skuBlock?.let { skuBlock ->
                    // 这里可以添加将商品添加到购物车的逻辑
                    // 或者跳转到商品详情页面
                    Toast.makeText(
                        context,
                        "点击了商品: ${skuBlock.title}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            },
            onSearch = { keyword, page, onResult ->
                viewModel.searchProducts(keyword, page, onResult)
            }
        )
    }

    if (viewModel.showAccountDialog.value) {
        AccountDialog(
            onDismiss = { viewModel.setShowAccountDialog(false) },
            // 数据状态
            cardList = viewModel.cardList.value,
            cardBalance = viewModel.cardBalance.value,
            couponList = viewModel.couponList.value,
            unavailableCouponList = viewModel.unavailableCouponList.value,
            boostCouponList = viewModel.boostCouponList.value,
            orderList = viewModel.orderList.value,
            hasNextPage = viewModel.hasNextPage.value,
            lastOrderId = viewModel.lastOrderId.value,
            invitationRewards = viewModel.invitationRewards.value,
            successInvites = viewModel.successInvites.value,
            creditData = viewModel.creditData.value,
            creditDetails = viewModel.creditDetails.value,
            creditCount = viewModel.creditCount.value,
            // 回调方法
            onGetAllCard = { viewModel.getAllCard() },
            onGetCardIndexInfo = { viewModel.getCardIndexInfo() },
            onShowSendCardDialog = { card -> viewModel.showSendCardDialog(card) },
            onSimpleSendCard = { card -> viewModel.simpleSendCard(card) },
            onCancelSendCard = { card -> viewModel.cancelSendCard(card) },
            onShowCancelSendCardDialog = { card -> viewModel.showCancelSendCardDialog(card) },
            onGetCouponList = { viewModel.getCouponList() },
            onShowCouponDetailDialog = { coupon -> viewModel.showCouponDetailDialog(coupon) },
            onGetBoostCouponList = { viewModel.getBoostCouponList() },
            onGetGameCode = { prizeId -> viewModel.getGameCode(prizeId) },
            onShowBoostCouponDetailDialog = { boostCoupon ->
                viewModel.showBoostCouponDetailDialog(
                    boostCoupon
                )
            },
            onGetOrderList = { lastOrderId -> viewModel.getOrderList(lastOrderId) },
            onGetOrderDetail = { orderId -> viewModel.getOrderDetail(orderId) },
            onClearOrder = { viewModel.setShowClearOrderConfirmDialog(true) },
            onGetInvitationRewards = { page, pageSize ->
                viewModel.getInvitationV2TotalRewardList(
                    page,
                    pageSize
                )
            },
            onGetSuccessInvites = { page, pageSize ->
                viewModel.getInvitationV2SuccessList(
                    page,
                    pageSize
                )
            },
            onGetCreditDetail = { page -> viewModel.getCreditDetail(page) },
            onPointTeam = { viewModel.getPointTeam() },
            onSignRewardRule = { viewModel.getSignRewardRule() },
            onShowJoinPointTeamDialog = { viewModel.setShowJoinPointTeamDialog(true) },
            onBindCard = {
                viewModel.setShowBindCardDialog(true)
            },
            onGetNewPersonCoupon = {
                // 显示新人优惠券对话框
                viewModel.setShowNewPersonCouponDialog(true)
            },
            onKindCoupon = {
                // 显示领取优惠券对话框
                viewModel.setShowKindCouponDialog(true)
            },
            onHandLuck = {
                // 显示拼手气对话框
                viewModel.setShowHandLuckDialog(true)
            },
            onEditBoostConfig = {
                // 显示助力券配置对话框
                viewModel.setShowBoostCouponConfigDialog(true)
            },
            onSaveBoostConfig = {
                viewModel.formatAndSaveBoostCouponHelpData()
            },
            onQueryOrder = {
                // 显示订单查询对话框
                viewModel.setShowOrderSearchDialog(true)
            },
            onGetInvitationRules = { viewModel.getInvitationV2ActiveInfo(type = "show_activity_rule") },
            onCopyInvitationCode = { viewModel.copyInvitationCode() },
            onBindInvitationCode = { viewModel.setShowBindInvitationCodeDialog(true) }
        )
    }

    if (viewModel.showOrderPlaceDialog.value && viewModel.orderPlaceResponse.value != null) {
        OrderPlaceDialog(
            orderPlaceResponse = viewModel.orderPlaceResponse.value!!,
            onDismiss = { viewModel.setShowOrderPlaceDialog(false) },
            onConfirmClick = { orderPlaceData, tempOrderComment, expectTime, genPayUrl ->
                viewModel.orderConfirm(
                    orderPlaceData,
                    tempOrderComment,
                    expectTime,
                    genPayUrl
                )
            },
            onOrderCommentAction = { viewModel.setOrderComment(it) },
            onOrderPlaceOptionsChanged = { selectedCoupon, selectedRedPacket, balancePayOption, pointPayOption, pickSelf ->
                // 调用ViewModel中的orderPlaceWithOptions方法，传递用户选择的选项，触发重新请求
                viewModel.orderPlaceWithOptions(
                    selectedCoupons = selectedCoupon,
                    selectedRedPackets = selectedRedPacket,
                    balancePayOption = balancePayOption,
                    pointPayOption = pointPayOption,
                    pickSelf = pickSelf
                )
            },
            onSaveOrderPlaceConfig = { selectedCoupon, selectedRedPacket, balancePayOption, pointPayOption, pickSelf, expectTime, productsTotalAmount, totalPaymentNew ->
                viewModel.saveOrderPlaceConfig(
                    selectedCoupon,
                    selectedRedPacket,
                    balancePayOption,
                    pointPayOption,
                    pickSelf,
                    expectTime,
                    productsTotalAmount,
                    totalPaymentNew
                )
            },
            onGenPayUrlChange = { viewModel.setGenPayUrl(it) },
            expectTime = viewModel.expectTime.value,
            orderComment = viewModel.orderComment.value,
            genPayUrl = viewModel.genPayUrl.value
        )
    }

    // 批量账号操作对话框
    if (showOrderTokensOperateDialog) {
        OperationDialog(
            title = "批量账号操作",
            operations = listOf(
                "查询状态",
                "查询余额",
                "查询积分",
                "查询订单",
                "清空地址",
                "清空购物车",
                "清空订单"
            ),
            onDismiss = { viewModel.setShowOrderTokensOperateDialog(false) },
            onConfirm = { selectedOps, multiThread, _ ->
                Log.d(
                    "OrderScreen",
                    "Selected account operations: $selectedOps, Multi-thread: $multiThread"
                )
                viewModel.setIsOrderTokensOperating(true)
                viewModel.setShowOrderTokensOperateDialog(false)
                // 调用BatchOperationUtils执行批量操作
                BatchOperationUtils.execute(
                    viewModel = viewModel,
                    operations = selectedOps,
                    type = BatchOperationUtils.OperationType.ACCOUNT,
                    multiThreadRangeList = multiThread,
                    context = context,
                    setCurrentOrderTokenIndex = { index ->
                        viewModel.setCurrentOperationOrderTokenIndex(index)
                    }
                )
                // 清空失败的账号序号
                FailedTokenIndexRecordUtils.clearFailedTokenIndexes(FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION)
            },
            showMultiThreadDialog = {
                viewModel.setShowMultiThreadDialog(
                    true,
                    FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                )
            },
            multiThreadRangeList = viewModel.multiThreadRangeList.value,
            multiThreadEnabled = viewModel.multiThreadEnabled.value,
            setMultiThreadEnabled = { viewModel.setMultiThreadEnabled(it) },
            fastenAddress = viewModel.fastenAddress.value,
            onFastenAddressChange = { viewModel.setFastenAddress(it) },
            showOrderExportDialog = {
                viewModel.setShowOrderExportDialog(true)
            }
        )
    }

    // 批量活动操作对话框
    if (showActivitiesOperateDialog) {
        OperationDialog(
            title = "批量活动操作",
            operations = listOf("积分组队", "助力券", "邀请有礼"),
            onDismiss = { viewModel.setShowActivitiesOperateDialog(false) },
            onConfirm = { selectedOps, multiThread, activityGetType ->
                Log.d(
                    "OrderScreen",
                    "Selected activity operations: $selectedOps, Multi-thread: $multiThread, Activity type: $activityGetType"
                )
                viewModel.setIsActivitiesOperating(true)
                viewModel.setShowActivitiesOperateDialog(false)
                viewModel.setActivityGetType(activityGetType)
                // 调用BatchOperationUtils执行批量操作
                BatchOperationUtils.execute(
                    viewModel = viewModel,
                    operations = selectedOps,
                    type = BatchOperationUtils.OperationType.ACTIVITY,
                    multiThreadRangeList = multiThread,
                    activityGetType = activityGetType,
                    context = context,
                    setCurrentOrderTokenIndex = { index ->
                        viewModel.setCurrentOperationOrderTokenIndex(index)
                    }
                )
                // 清空失败的账号序号
                FailedTokenIndexRecordUtils.clearFailedTokenIndexes(FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION)
            },
            showMultiThreadDialog = {
                viewModel.setShowMultiThreadDialog(
                    true,
                    FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                )
            },
            multiThreadRangeList = viewModel.multiThreadRangeList.value,
            multiThreadEnabled = viewModel.multiThreadEnabled.value,
            setMultiThreadEnabled = { viewModel.setMultiThreadEnabled(it) },
            boostCouponHelpData = viewModel.boostCouponHelpData.value,
            fastenAddress = viewModel.fastenAddress.value,
            onFastenAddressChange = { viewModel.setFastenAddress(it) },
            activityGetType = viewModel.activityGetType.value
        )
    }

    // 确认清空账号对话框
    if (viewModel.showClearAllTokensConfirmDialog.value) {
        RangeConfirmationDialog(
            title = "确认清空账号",
            message = "请选择要删除的账号范围或清空全部账号",
            onConfirmAll = {
                viewModel.clearAllOrderTokens()
                viewModel.setShowClearAllTokensConfirmDialog(false)
            },
            onConfirmRange = { startIndex, endIndex ->
                viewModel.clearOrderTokensRange(startIndex, endIndex)
                viewModel.setShowClearAllTokensConfirmDialog(false)
            },
            onDismiss = { viewModel.setShowClearAllTokensConfirmDialog(false) },
            totalAccounts = orderTokens.size
        )
    }

    // 确认删除购物车商品对话框
    if (viewModel.showDeleteCartItemConfirmDialog.value) {
        ConfirmationDialog(
            title = "确认删除商品",
            message = "确定要从购物车中删除此商品吗？",
            onConfirm = {
                viewModel.cartItemProductToDelete.value?.let { product ->
                    viewModel.deleteCartItem(product)
                }
                viewModel.setShowDeleteCartItemConfirmDialog(false, null)
            },
            onDismiss = { viewModel.setShowDeleteCartItemConfirmDialog(false, null) }
        )
    }

    // 确认清空地址对话框
    if (viewModel.showClearAddressConfirmDialog.value) {
        ConfirmationDialog(
            title = "确认清空地址",
            message = "确定要清空当前账号的地址吗？",
            onConfirm = {
                viewModel.clearAddress() // 假设ViewModel中有此方法
                viewModel.clearAddressConfirmDialog(false)
            },
            onDismiss = { viewModel.clearAddressConfirmDialog(false) }
        )
    }

    // 确认清空购物车对话框
    if (viewModel.showClearCartConfirmDialog.value) {
        ConfirmationDialog(
            title = "确认清空购物车",
            message = "确定要清空购物车中的所有商品吗？",
            onConfirm = {
                viewModel.clearCart() // 假设ViewModel中有此方法
                viewModel.setShowClearCartConfirmDialog(false)
            },
            onDismiss = { viewModel.setShowClearCartConfirmDialog(false) }
        )
    }

    // 确认删除单个地址对话框
    if (viewModel.showDeleteAddressConfirmDialog.value && addressToDelete != null) {
        ConfirmationDialog(
            title = "确认删除地址",
            message = "确定要删除该地址吗？",
            onConfirm = {
                addressToDelete?.let { addressItem ->
                    viewModel.deleteAddressById(addressItem)
                }
                viewModel.setShowDeleteAddressConfirmDialog(false, null)
            },
            onDismiss = { viewModel.setShowDeleteAddressConfirmDialog(false, null) }
        )
    }

    // 确认清空订单对话框
    if (viewModel.showClearOrderConfirmDialog.value) {
        ConfirmationDialog(
            title = "确认清空订单",
            message = "将清空已完成、已取消、已关闭的订单",
            onConfirm = {
                viewModel.clearOrder()
                viewModel.setShowClearOrderConfirmDialog(false)
            },
            onDismiss = { viewModel.setShowClearOrderConfirmDialog(false) }
        )
    }

    // 添加账号对话框
    if (viewModel.showAddAccountDialog.value) {
        AddAccountDialog(
            clipboardContent = viewModel.addAccountContent.value,
            onDismiss = { viewModel.setShowAddAccountDialog(false) },
            onConfirm = { content ->
                viewModel.addOrderToken(content)
                viewModel.setShowAddAccountDialog(false)
            }
        )
    }

    // 添加地址对话框
    if (viewModel.showAddAddressDialog.value && currentSelectedAccount != null) {
        AddAddressDialog(
            addAddressStatus = viewModel.showAddAddressDialog.value,
            currentAccount = currentSelectedAccount!!,
            phone = currentSelectedAccount?.phoneNumber ?: "",
            addAddressStr = viewModel.addAddressStr.value,
            addAddressLocationStr = viewModel.addAddressLocationStr.value,
            randomDetailsRegex = viewModel.randomDetailsRegex.value,
            fastenAddress = viewModel.fastenAddress.value,
            fastenLatLng = viewModel.fastenLatLng.value,
            orderConfirmComment = viewModel.orderComment.value,
            addAddressShopItem = viewModel.addAddressShopItem.value,
            onFastenAddressChange = { viewModel.setFastenAddress(it) },
            onFastenLatLngChange = { viewModel.setFastenLatLng(it) },
            onAddressSubmit = { addressStr, locationStr, name, phone ->
                viewModel.addAddress(addressStr, locationStr, name, phone)
            },
            onAddressConfigSave = { addressStr, locationStr, detailsRegex, comment, shopItem ->
                viewModel.saveAddressConfig(
                    addressStr,
                    locationStr,
                    detailsRegex,
                    comment,
                    shopItem
                )
            },
            onDismiss = { viewModel.setShowAddAddressDialog(false) },
            getPreConfigAddressList = { viewModel.getPreConfigAddressList() },
            onCopyAddressConfig = { inputStr ->
                viewModel.onCopyConfig(inputStr, "AddAddressDialog")
            },
            onPasteAddressConfig = {
                viewModel.handleClipboardTextAddAddress()
            }
        )
    }

    // 添加商品对话框
    if (viewModel.showAddProductDialog.value) {
        AddProductDialog(
            productConfigStr = viewModel.productConfigStr.value,
            onDismiss = { viewModel.setShowAddProductDialog(false) },
            onConfirm = { inputString ->
                viewModel.addProductToCart(inputString)
                viewModel.setShowAddProductDialog(false)
            },
            onProductConfigSave = { inputString ->
                viewModel.saveProductConfig(inputString)
            },
            onCopyProductConfig = { inputString ->
                viewModel.onCopyConfig(inputString, "AddProductDialog")
            },
            onPasteProductConfig = {
                viewModel.handleClipboardTextAddProduct()
            }
        )
    }

    // 代理选择对话框
    if (viewModel.showProxySelectDialog.value) {
        ProxySelectDialog(
            onDismiss = {
                viewModel.setEnableProxy(false)
                viewModel.setShowProxySelectDialog(false)
            },
            onProxySelected = { proxyName ->
                viewModel.setSelectedProxy(proxyName)
                viewModel.setShowProxySelectDialog(false)
            }
        )
    }

    // 绑定永辉卡对话框
    if (viewModel.showBindCardDialog.value) {
        BindCardDialog(
            onDismiss = { viewModel.setShowBindCardDialog(false) },
            onBindCard = { cardInfo ->
                viewModel.bindCardOrLink(cardInfo)
                viewModel.setShowBindCardDialog(false)
            },
            onReceiveCard = { cardInfo ->
                viewModel.receiveCard(cardInfo)
            },
            onValidateCard = { cardInfo, callback ->
                viewModel.validateCardOrLink(cardInfo) { balance ->
                    callback(balance)
                }
            }
        )
    }

    // 新人优惠券对话框
    if (viewModel.showNewPersonCouponDialog.value) {
        NewPersonCouponDialog(
            onDismiss = { viewModel.setShowNewPersonCouponDialog(false) },
            currentShopList = viewModel.shopList.value,
            newPersonCouponShopParams = viewModel.newPersonCouponShopParams.value,
            onSaveConfig = { viewModel.saveNewPersonCouponShopParams(it) },
            onGetActivityRule = { sellerId, shopId ->
                viewModel.getNewPersonCouponInfo(sellerId, shopId) { rule ->
                    viewModel.setCommonActivityRule(rule)
                    viewModel.setShowCommonActivityRuleDialog(true)
                }
            },
            onConfirm = { sellerId, shopId ->
                viewModel.getNewPersonCoupon(sellerId, shopId)
                viewModel.setShowNewPersonCouponDialog(false)
            },
            getPreConfigShopParams = { viewModel.getPreConfigShopParams() }
        )
    }

    // 新人券参数快速设置对话框
    if (viewModel.showNewPersonCouponShopParamsFastSetDialog.value) {
        NewPersonCouponShopParamsFastSetDialog(
            onDismiss = { viewModel.setShowNewPersonCouponShopParamsFastSetDialog(false) },
            newPersonCouponShopParams = viewModel.newPersonCouponShopParams.value,
            preConfigShopParams = { viewModel.getPreConfigShopParams() },
            onSaveConfig = {
                viewModel.saveNewPersonCouponShopParams(it)
                viewModel.setShowNewPersonCouponShopParamsFastSetDialog(false)
            }
        )
    }

    // 活动规则对话框
    if (viewModel.showCommonActivityRuleDialog.value) {
        ActivityRuleDialog(
            rule = viewModel.commonActivityRule.value,
            onDismiss = { viewModel.setShowCommonActivityRuleDialog(false) }
        )
    }

    // 领取优惠券对话框
    if (viewModel.showKindCouponDialog.value) {
        KindCouponDialog(
            onDismiss = { viewModel.setShowKindCouponDialog(false) },
            couponPromotionCodeStr = viewModel.couponPromotionCodeStr.value,
            onSaveConfig = { viewModel.saveCouponPromotionCode(it) },
            onGetNewUserCouponInfo = { callback ->
                viewModel.getNewUserCouponCode {
                    callback(it)
                }
            },
            onConfirm = { couponCodes ->
                couponCodes.forEach { couponCode ->
                    if (couponCode.isNotEmpty()) {
                        if (couponCode.startsWith("GROUP_NEW_USER_COUPON")) {
                            val couponCodeArr = couponCode.split(",")
                            if (couponCodeArr.size >= 2) {
                                viewModel.getNewUserCouponReceive(couponCodeArr[1])
                            }
                        } else {
                            viewModel.kindCoupon(couponCode)
                        }
                    }
                }
                viewModel.setShowKindCouponDialog(false)
            },
            getPreConfigKindCouponList = { viewModel.getPreConfigKindCouponList() }
        )
    }

    // 拼手气对话框
    if (viewModel.showHandLuckDialog.value) {
        HandLuckDialog(
            onDismiss = { viewModel.setShowHandLuckDialog(false) },
            handLuckEventIdStr = viewModel.handLuckEventIdStr.value,
            onEventIdChange = { viewModel.setHandLuckEventIdStr(it) },
            onGetActivityRule = { eventId -> viewModel.handLuckGetSlotMachineData(eventId) },
            onGetActivityRewardList = { eventId -> viewModel.handLuckGetTotalRewardList(eventId) },
            onConfirm = { eventId -> viewModel.handLuckDrawLottery(eventId) }
        )
    }

    // 助力券配置对话框
    if (viewModel.showBoostCouponConfigDialog.value) {
        BoostCouponConfigDialog(
            onDismiss = { viewModel.setShowBoostCouponConfigDialog(false) },
            boostCouponHelpData = viewModel.boostCouponHelpData.value,
            onSaveConfig = { viewModel.saveBoostCouponHelpData(it) }
        )
    }

    // 抢券配置对话框
    if (viewModel.showCouponGrabbingConfigDialog.value) {
        CouponGrabbingConfigDialog(
            onDismiss = { viewModel.setShowCouponGrabbingConfigDialog(false) },
            couponGrabbingConfig = viewModel.couponGrabbingConfig.value,
            onShowMultiThreadDialog = {
                viewModel.setShowMultiThreadDialog(
                    true,
                    FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION
                )
            },
            multiThreadRangeList = viewModel.multiThreadRangeList.value,
            multiThreadEnabled = viewModel.multiThreadEnabled.value,
            setMultiThreadEnabled = { viewModel.setMultiThreadEnabled(it) },
            onSaveConfig = { viewModel.saveCouponGrabbingConfig(it) },
            onStartGrabbing = { requestBody, isTimer, targetTime, multiThreadRangeList ->
                CouponGrabbingUtils.startCouponGrabbing(
                    requestBody = requestBody,
                    isTimer = isTimer,
                    targetTime = targetTime,
                    multiThreadRangeList = multiThreadRangeList,
                    viewModel = viewModel,
                    selectedProxy = if (viewModel.enableProxy.value) viewModel.selectedProxy.value else "",
                    context = context,
                    waitTime = { waitTime ->
                        viewModel.setCouponGrabbingRemainingTime(waitTime)
                    }
                )
            },
            fastenAddress = viewModel.fastenAddress.value,
            onFastenAddressChange = { viewModel.setFastenAddress(it) }
        )
    }

    // 订单查询对话框
    if (viewModel.showOrderSearchDialog.value) {
        OrderSearchDialog(
            onDismiss = { viewModel.setShowOrderSearchDialog(false) },
            cardRecordOrderId = viewModel.recordOrderId.value,
            onCardRecordOrderIdChange = { viewModel.setRecordOrderId(it) },
            onGetCardRecordOrderList = { viewModel.getCardRecordOrderList() },
            onGetAfterSaleRecordOrderList = { viewModel.getAfterSalesList() },
            onGetInvoiceRecordOrderList = { viewModel.getInvoiceCanApplyOrderList() },
            onShowDeletedOrderRecords = { viewModel.loadDeletedOrderRecords() },
            onConfirm = { orderId ->
                if (orderId.isNotEmpty() && orderId.length == 16) {
                    viewModel.getOrderDetail(orderId)
                } else {
                    Toast.makeText(context, "请输入正确的订单号", Toast.LENGTH_SHORT).show()
                }
            }
        )
    }

    // 卡记录订单列表对话框
    if (viewModel.showCardRecordOrderListDialog.value) {
        CardRecordOrderListDialog(
            onDismiss = { viewModel.setShowCardRecordOrderListDialog(false) },
            cardRecordOrderList = viewModel.cardRecordOrderList.value,
            onOrderSelect = { orderId ->
                viewModel.setRecordOrderId(orderId)
                viewModel.setShowCardRecordOrderListDialog(false)
            },
            hasNextPage = viewModel.cardRecordOrderListHasNextPage.value,
            onLoadMore = {
                if (viewModel.cardRecordOrderListHasNextPage.value) {
                    val nextPage =
                        (viewModel.cardRecordOrderListCurrentPage.value.toInt() + 1).toString()
                    viewModel.getCardRecordOrderList(nextPage, true)
                }
            }
        )
    }

    // 发票可申请订单列表对话框
    if (viewModel.showInvoiceCanApplyOrderListDialog.value) {
        InvoiceCanApplyOrderListDialog(
            onDismiss = { viewModel.setShowInvoiceCanApplyOrderListDialog(false) },
            orderList = viewModel.invoiceCanApplyOrderList.value,
            page = viewModel.invoiceCanApplyOrderPage.value,
            pageCount = viewModel.invoiceCanApplyOrderPageCount.value,
            onLoadMore = { nextPage -> viewModel.getInvoiceCanApplyOrderList(nextPage) },
            onSetInvoiceOrderId = { orderId ->
                viewModel.setRecordOrderId(orderId)
                viewModel.setShowInvoiceCanApplyOrderListDialog(false)
            }
        )
    }

    // 售后订单列表对话框
    if (viewModel.showAfterSalesListDialog.value) {
        AfterSalesListDialog(
            onDismiss = { viewModel.setShowAfterSalesListDialog(false) },
            orderList = viewModel.afterSalesOrderList.value,
            page = viewModel.afterSalesOrderPage.value,
            pageCount = viewModel.afterSalesOrderPageCount.value,
            onLoadMore = { nextPage -> viewModel.getAfterSalesList(nextPage) },
            onSetAfterSalesOrderId = { orderId ->
                viewModel.setRecordOrderId(orderId)
                viewModel.setShowAfterSalesListDialog(false)
            }
        )
    }

    // 订单详情对话框
    viewModel.orderDetailData.value?.let { orderDetail ->
        OrderDetailDialog(
            orderDetail = orderDetail,
            onDismiss = { viewModel.setOrderDetailData(null) },
            onSaveProductConfig = { viewModel.saveOrderProductConfig(it) },
            onOrderPrePay = { orderId, payType ->
                viewModel.orderPrePay(
                    orderId,
                    payType
                )
            },
            onUpdateDelivery = { orderId, shopId ->
                viewModel.checkCanUpdateDeliveryInfo(
                    orderId,
                    shopId
                )
            },
            onCancelOrder = { orderId -> viewModel.cancelOrder(orderId) },
            onRefundOrder = { orderId -> viewModel.refundOrder(orderId) },
            onDeleteOrder = { orderId -> viewModel.deleteOrder(orderId) },
            onRedEnvelope = { orderId -> viewModel.orderRedEnvelope(orderId) },
            onViewDeliveryPhotos = { viewModel.setDeliveryPhotos(it) }
        )
    }

    // 送达照片对话框
    if (viewModel.showDeliveryPhotosDialog.value) {
        DeliveryPhotosDialog(
            photos = viewModel.deliveryPhotos.value,
            onDismiss = { viewModel.setShowDeliveryPhotosDialog(false) }
        )
    }

    // 添加取消赠送永辉卡确认对话框
    if (viewModel.showCancelSendCardDialog.value) {
        CancelSendCardDialog(
            onDismiss = { viewModel.setShowCancelSendCardDialog(false) },
            onConfirm = { card -> viewModel.cancelSendCard(card) },
            card = viewModel.selectedCardToCancel.value
        )
    }

    // 添加赠送永辉卡确认对话框
    if (viewModel.showSendCardDialog.value) {
        SendCardDialog(
            onDismiss = { viewModel.setShowSendCardDialog(false) },
            onConfirm = { card -> viewModel.sendCard(card) },
            card = viewModel.selectedCardToSend.value
        )
    }

    // 优惠券详情对话框
    if (viewModel.showCouponDetailDialog.value) {
        CouponDetailDialog(
            onDismiss = { viewModel.setShowCouponDetailDialog(false) },
            coupon = viewModel.selectedCoupon.value
        )
    }

    // 助力券详情对话框
    if (viewModel.showBoostCouponDetailDialog.value && viewModel.selectedBoostCoupon.value != null) {
        BoostCouponDetailDialog(
            onDismiss = { viewModel.setShowBoostCouponDetailDialog(false) },
            boostCoupon = viewModel.selectedBoostCoupon.value
        )
    }

    // 修改订单配送信息弹窗
    if (viewModel.showOrderDeliveryInfoDialog.value) {
        OderDeliveryChangeDialog(
            onDismiss = { viewModel.setShowOrderDeliveryInfoDialog(false) },
            oderDeliveryInfoData = viewModel.orderDeliveryInfo.value!!,
            service = viewModel.requestService!!,
            orderId = viewModel.orderDetailData.value?.baseinfo?.id!!,
            oderDeliveryAddressList = viewModel.availableAddress.value,
            returnParam = { viewModel.updateDeliveryInfo(it) }
        )
    }

    // 绑定邀请码对话框
    if (viewModel.showBindInvitationCodeDialog.value) {
        BindInvitationCodeDialog(
            onDismiss = { viewModel.setShowBindInvitationCodeDialog(false) },
            bindInvitationCodeStr = viewModel.bindInvitationCode.value,
            onBindInvitationCode = { invitationCode -> viewModel.bindInvitationCode(invitationCode) },
            onSaveInvitationCode = { invitationCode -> viewModel.saveInvitationCode(invitationCode) }
        )
    }

    // 拼手气奖励列表对话框
    if (viewModel.showHandLuckRewardListDialog.value) {
        HandLuckRewardListDialog(
            onDismiss = { viewModel.setShowHandLuckRewardListDialog(false) },
            rewardList = viewModel.handLuckRewardList.value
        )
    }

    // 加入组队瓜分对话框
    if (viewModel.showJoinPointTeamDialog.value) {
        JoinPointTeamDialog(
            onDismiss = { viewModel.setShowJoinPointTeamDialog(false) },
            pointTeamCode = viewModel.pointTeamCode.value,
            onPointTeamCodeChange = { viewModel.setPointTeamCode(it) },
            onConfirm = { teamCode -> viewModel.joinPointTeam(teamCode) }
        )
    }

    // 自动下单对话框
    if (viewModel.showOrderAutoPlaceDialog.value) {
        OrderAutoPlaceDialog(
            onDismiss = {
                viewModel.setShowOrderAutoPlaceDialog(false)
                // 移除这里的 setAutoOrderPlace(false) 调用
                // 因为它会与 autoOrderPlace 函数中的 setAutoOrderPlace(true) 产生竞争条件
            },
            onCancel = {
                viewModel.setShowOrderAutoPlaceDialog(false)
                viewModel.setAutoOrderPlace(false)
            },
            autoOrderPlaceData = AutoOrderPlaceData(
                addAddressStr = viewModel.addAddressStr.value,
                addAddressLocationStr = viewModel.addAddressLocationStr.value,
                randomDetailsRegex = viewModel.randomDetailsRegex.value,
                balancePayOption = viewModel.balancePayOption.value,
                pointPayOption = viewModel.pointPayOption.value,
                couponPromotionCodeStr = viewModel.couponPromotionCodeStr.value,
                newPersonCouponShopParams = viewModel.newPersonCouponShopParams.value,
                productConfigStr = viewModel.productConfigStr.value,
                expectTime = viewModel.expectTime.value,
                isPickSelf = viewModel.isPickSelf.value,
                orderComment = viewModel.orderComment.value,
                selectedOrderPlaceCoupon = viewModel.selectedOrderPlaceCoupon.value,
                selectedOrderPlaceRedPacket = viewModel.selectedOrderPlaceRedPacket.value,
                productsTotalAmount = viewModel.productsTotalAmount.value,
                totalPaymentNew = viewModel.totalPaymentNew.value,
                addAddressShopItem = viewModel.addAddressShopItem.value
            ),
            onConfirm = { autoOrderPlaceData, isAccountManager, isAddAddress, isAddCart, isPlaceOrder, multiThreadRangeList, operationIntervalMs, genPayUrl ->
                autoOrderPlace(
                    autoOrderPlaceData = autoOrderPlaceData,
                    isAccountManager = isAccountManager,
                    isAddAddress = isAddAddress,
                    isAddCart = isAddCart,
                    isPlaceOrder = isPlaceOrder,
                    multiThreadRangeList = multiThreadRangeList,
                    viewModel = viewModel,
                    selectedProxy = if (enableProxy) selectedProxy else "",
                    context = context,
                    setAutoOrderPlace = { viewModel.setAutoOrderPlace(it) },
                    operationIntervalMs = operationIntervalMs,
                    genPayUrl = genPayUrl
                )
            },
            showMultiThreadDialog = {
                viewModel.setShowMultiThreadDialog(
                    true,
                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION
                )
            },
            multiThreadRangeList = viewModel.multiThreadRangeList.value,
            multiThreadEnabled = viewModel.multiThreadEnabled.value,
            setMultiThreadEnabled = { viewModel.setMultiThreadEnabled(it) },
            showOperationIntervalDialog = {
                viewModel.setShowOperationIntervalDialog(true)
            },
            operationIntervalMs = viewModel.operationIntervalMs.value,
            onGenPayUrlChange = { viewModel.setGenPayUrl(it) },
            genPayUrl = viewModel.genPayUrl.value
        )
    }

    // 自动下单多线程配置对话框
    if (viewModel.showMultiThreadDialog.value) {
        MultiThreadDialog(
            onDismiss = { viewModel.setShowMultiThreadDialog(false) },
            multiThreadRangeListStr = viewModel.multiThreadRangeListStr.value,
            onConfirm = { multiThreadRangeList, multiThreadRangeListStr ->
                viewModel.setMultiThreadRangeList(multiThreadRangeList)
                viewModel.setMultiThreadRangeListStr(multiThreadRangeListStr)
                viewModel.setShowMultiThreadDialog(false)
                viewModel.setMultiThreadEnabled(true)
            },
            maxThread = orderTokens.size
        )
    }

    // 操作间隔设置对话框
    if (viewModel.showOperationIntervalDialog.value) {
        OperationIntervalDialog(
            onDismiss = { viewModel.setShowOperationIntervalDialog(false) },
            onConfirm = { intervalMs ->
                viewModel.setOperationIntervalMs(intervalMs)
                viewModel.setShowOperationIntervalDialog(false)
            },
            currentIntervalMs = viewModel.operationIntervalMs.value
        )
    }

    // 导出订单弹窗
    if (viewModel.showOrderExportDialog.value) {
        OrderExportDialog(
            onDismiss = { viewModel.setShowOrderExportDialog(false) },
            onConfirm = { filters ->
                OrderDataCacheManager.exportOrders(context, filters)
            }
        )
    }

    // 显示导出账号筛选对话框
    if (isExportAccountDialogVisible) {
        ExportOrderAccountDialog(
            onDismiss = { viewModel.setExportAccountDialogVisible(false) },
            onConfirm = { filterOptions, addParamsToNote ->
                viewModel.onExportAccountsConfirm(filterOptions, addParamsToNote)
                val timeStamp = SimpleDateFormat(
                    "yyyyMMddHHmmss",
                    Locale.getDefault()
                ).format(Date())
                accountExportFileLauncher.launch("accounts${timeStamp}.txt")
            },
            addParamsToNote = viewModel.addParamsToNote.value,
            tokens = orderTokens
        )
    }

    // 删除订单记录对话框
    if (showDeletedOrderRecordsDialog) {
        DeletedOrderRecordsDialog(
            onDismiss = { viewModel.hideDeletedOrderRecordsDialog() },
            deletedOrders = deletedOrderRecords,
            onClearAllRecords = { viewModel.clearAllDeletedOrderRecords() },
            onOrderSelect = { orderId ->
                viewModel.setRecordOrderId(orderId)
                viewModel.hideDeletedOrderRecordsDialog()
            }
        )
    }
}

@Composable
fun SectionLabelText(label: String, modifier: Modifier = Modifier) {
    Text(
        text = label,
        fontSize = 12.sp,
        maxLines = 1,
        modifier = modifier,
        lineHeight = 12.sp
    )
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun AccountCard(
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    displayText: String,
    topStartText: String,
    bottomEndText: String
) {
    Card(
        modifier = Modifier
            .padding(start = 10.dp, end = 8.dp, bottom = 5.dp)
            .height(54.dp),
        colors = cardThemeOverlay()
    ) {
        Box {
            Row(
                modifier = Modifier
                    .combinedClickable(
                        onClick = onClick,
                        onLongClick = onLongClick
                    )
                    .padding(start = 5.dp)
                    .fillMaxWidth()
                    .fillMaxHeight(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Row(
                    modifier = Modifier
                        .weight(1f) // 使用weight来占用剩余空间
                ) {
                    Text(
                        text = displayText,
                        fontSize = 15.sp,
                        maxLines = 1,
                        modifier = Modifier
                            .horizontalScroll(rememberScrollState())
                    )
                }
            }

            Text(
                text = bottomEndText,
                fontSize = 10.sp,
                color = Color.Gray,
                lineHeight = 10.sp,
                modifier = Modifier
                    .horizontalScroll(rememberScrollState())
                    .align(Alignment.BottomEnd)
                    .clickable { }
                    .padding(2.dp),
            )

            Text(
                text = topStartText,
                fontSize = 10.sp,
                color = Color.Gray,
                lineHeight = 10.sp,
                modifier = Modifier
                    .horizontalScroll(rememberScrollState())
                    .align(Alignment.TopStart)
                    .padding(start = 5.dp, top = 2.dp)
            )
        }
    }
}

@Composable
fun CartCard(
    displayText: String,
    viewModel: OrderViewModel,
    onCardClick: (() -> Unit),
    actions: @Composable () -> Unit,
    @SuppressLint("ModifierParameter") modifier: Modifier = Modifier
) {
    Card(
        modifier = Modifier
            .padding(start = 10.dp, end = 8.dp, bottom = 5.dp)
            .height(54.dp),
        colors = cardThemeOverlay()
    ) {
        Row(
            modifier = Modifier
                .let {
                    if (!viewModel.fastenAddress.value) it.clickable(onClick = onCardClick)
                    else it
                }
                .padding(start = 5.dp)
                .fillMaxWidth()
                .fillMaxHeight(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier.weight(1f) // 使用weight来占用剩余空间
            ) {
                Text(
                    text = displayText,
                    fontSize = 15.sp,
                    maxLines = 1,
                    modifier = Modifier
                        .horizontalScroll(rememberScrollState())
                )
            }
            Row(
                modifier = modifier
            ) {
                actions()
            }
        }
    }
}

@Composable
fun ActionButton(
    icon: ImageVector,
    onClick: () -> Unit,
    enabled: Boolean = true,
    @SuppressLint("ModifierParameter") modifier: Modifier = Modifier
) {
    IconButton(
        onClick = onClick,
        enabled = enabled,
        modifier = modifier
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = if (!enabled) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
            else MaterialTheme.colorScheme.onSurface
        )
    }
}

@Composable
fun OrderTokenItem(
    orderToken: OrderTokenEntity,
    isClicked: Boolean,
    isLongClicked: Boolean,
    isCurrentOperationOrderToken: Boolean,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    onDoubleClick: () -> Unit,
    isSearchResult: Boolean = false,
    progressRecord: String,
    index: Int? = null // 添加序号参数
) {
    // 根据点击状态选择背景颜色
    val targetColor = when {
        isSearchResult -> Color(0xFFC5CAE9) // 搜索结果高亮颜色
        isClicked -> Color(0xCBF7DEF4) // 点击后的颜色标记
        isLongClicked -> Color(0xCCBBDEFB) // 长按后的颜色标记
        isCurrentOperationOrderToken -> Color(0xCCFBBBD7) // 当前操作的账号颜色标记
        else -> CardContainerColor // 默认颜色
    }

    // 使用animateColorAsState为颜色变化添加动画效果
    val backgroundColor by animateColorAsState(
        targetValue = targetColor,
        animationSpec = tween(
            durationMillis = 200,
            easing = FastOutSlowInEasing
        ),
        label = "backgroundColor"
    )

    TokenCard(
        backgroundColor = backgroundColor,
        onClick = onClick,
        onLongClick = onLongClick,
        onDoubleClick = onDoubleClick,
        content = {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
            ) {
                Text(
                    text = orderToken.phoneNumber + ",${orderToken.uid},${orderToken.updateDate}",
                    maxLines = 1,
                    modifier = Modifier
                        .padding(
                            start = 8.dp,
                            end = 8.dp,
                            top = 18.dp,
                            bottom = 18.dp
                        )
                        .horizontalScroll(rememberScrollState()),
                )
                // 显示序号（如果有）
                index?.let {
                    Text(
                        text = "#$it",
                        fontSize = 12.sp,
                        lineHeight = 12.sp,
                        color = Color.Unspecified.copy(0.3f),
                        modifier = Modifier
                            .align(Alignment.TopStart)
                            .padding(start = 4.dp, top = 2.dp)
                    )
                }

                Text(
                    text = progressRecord.ifEmpty { orderToken.extraNote },
                    maxLines = 1,
                    fontSize = 12.sp,
                    lineHeight = 12.sp,
                    overflow = TextOverflow.Ellipsis,
                    color = Color.Unspecified.copy(0.8f),
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(start = 8.dp, end = 4.dp, bottom = 2.dp)
                )
            }
        }
    )
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun TokenCard(
    backgroundColor: Color,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    // 可选双击事件，默认不使用
    onDoubleClick: () -> Unit = {},
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier
            .padding(horizontal = 10.dp, vertical = 4.dp)
            .fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor,
        )
    ) {
        Column(
            modifier = Modifier
                .combinedClickable(
                    onClick = onClick,
                    onLongClick = onLongClick,
                    onDoubleClick = onDoubleClick
                )
                .fillMaxWidth()
        ) {
            content()
        }
    }
}

@Composable
fun AccountListHeader(
    title: String,
    onClear: () -> Unit,
    onSearch: () -> Unit,
    onTokensOperation: () -> Unit,
    onActivitiesOperation: () -> Unit,
    onShowNewPersonCouponShopParamsFastSetDialog: () -> Unit,
    showSearchFrame: Boolean,
    isOrderTokensOperating: Boolean,
    isActivitiesOperating: Boolean,
    isCouponGrabbing: Boolean,
    onShowHelpCouponConfigDialog: () -> Unit,
    onShowCouponGrabbingConfigDialog: () -> Unit
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            modifier = Modifier
                .padding(
                    start = 15.dp,
                    end = 12.dp,
                    top = 8.dp,
                    bottom = 8.dp
                ),
            fontSize = 14.sp,
        )

        val iconSize = 24

        Row(
            modifier = Modifier
                .horizontalScroll(rememberScrollState())
                .padding(horizontal = 12.dp)
        ) {
            ActionIconButton(
                icon = if (!showSearchFrame) R.drawable.baseline_search_24 else R.drawable.baseline_close_24,
                contentDescription = "搜索按钮",
                text = if (!showSearchFrame) "搜索" else "关闭",
                iconSize = iconSize,
                onClick = onSearch
            )

            ActionIconButton(
                icon = R.drawable.baseline_delete_forever_24,
                contentDescription = "清空",
                text = "清空",
                iconSize = iconSize,
                onClick = onClear,
                isEnabled = !isOrderTokensOperating && !isActivitiesOperating
            )

            ActionIconButton(
                icon = if (!isOrderTokensOperating) R.drawable.outline_manage_accounts_24 else R.drawable.baseline_pause_circle_24,
                contentDescription = "批量账号操作",
                text = if (!isOrderTokensOperating) "账号操作" else "暂停",
                iconSize = iconSize,
                onClick = onTokensOperation,
                isEnabled = !isActivitiesOperating
            )

            ActionIconButton(
                icon = if (!isActivitiesOperating) R.drawable.outline_multiline_chart_24 else R.drawable.baseline_pause_circle_24,
                contentDescription = "批量活动操作",
                text = if (!isActivitiesOperating) "活动操作" else "暂停",
                iconSize = iconSize,
                onClick = onActivitiesOperation,
                isEnabled = !isOrderTokensOperating
            )

            ActionIconButton(
                icon = R.drawable.outline_fiber_new_24,
                contentDescription = "新人券配置",
                text = "新人券",
                iconSize = iconSize,
                onClick = onShowNewPersonCouponShopParamsFastSetDialog
            )

            ActionIconButton(
                icon = R.drawable.outline_box_edit_24,
                contentDescription = "助力券配置",
                text = "助力券",
                iconSize = iconSize,
                onClick = onShowHelpCouponConfigDialog
            )

            ActionIconButton(
                icon = if (!isCouponGrabbing) R.drawable.outline_timer_24 else R.drawable.baseline_pause_circle_24,
                contentDescription = "抢券功能",
                text = if (!isCouponGrabbing) "抢券" else "暂停",
                iconSize = iconSize,
                onClick = onShowCouponGrabbingConfigDialog,
                isEnabled = !isOrderTokensOperating
            )
        }
    }
}

@Composable
fun ActionIconButton(
    icon: Int,
    contentDescription: String,
    text: String = "",
    iconSize: Int = 24,
    onClick: () -> Unit,
    isEnabled: Boolean = true
) {
    TextButton(onClick = onClick, enabled = isEnabled) {
        Box {
            Icon(
                painter = painterResource(id = icon),
                contentDescription = contentDescription,
                tint = Color.Black.copy(alpha = 0.6f),
                modifier = Modifier.size(iconSize.dp)
            )
            if (text.isNotEmpty()) {
                Text(
                    text = text,
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .clip(RoundedCornerShape(2.dp))
                        .background(Color.White),
                    fontSize = 7.sp,
                    lineHeight = 7.sp,
                    color = Color.Black.copy(alpha = 0.6f)
                )
            }
        }
    }
}

/**
 * 地址卡片组件，包含地址信息和下拉菜单
 */
@Composable
fun AddressCard(
    displayText: String,
    viewModel: OrderViewModel,
    selectedAddressItem: AddressItem?,
    showAddressMenu: Boolean,
    addressList: List<AddressItem>,
    onCardClick: () -> Unit,
    onDismissMenu: () -> Unit,
    onSelectAddress: (AddressItem) -> Unit,
    onDeleteAddress: (AddressItem) -> Unit,
    onSaveAddress: (AddressItem) -> Unit,
    actions: @Composable () -> Unit,
    @SuppressLint("ModifierParameter") modifier: Modifier = Modifier
) {
    Card(
        modifier = Modifier
            .padding(start = 10.dp, end = 8.dp, bottom = 5.dp)
            .height(54.dp),
        colors = CardDefaults.cardColors(
            CardContainerColor,
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .let {
                    if (!viewModel.fastenAddress.value) it.clickable(onClick = onCardClick)
                    else it
                },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Box(
                modifier = Modifier.weight(1f)
            ) {
                Row {
                    Text(
                        text = displayText,
                        fontSize = 15.sp,
                        maxLines = 1,
                        modifier = Modifier
                            .padding(start = 5.dp)
                            .horizontalScroll(rememberScrollState())
                    )
                }

                // 地址下拉菜单
                DropdownMenu(
                    expanded = showAddressMenu,
                    onDismissRequest = onDismissMenu,
                    modifier = Modifier.heightIn(max = 200.dp)
                ) {
                    if (addressList.isNotEmpty()) {
                        addressList.forEach { addressItem ->
                            val area = addressItem.address.area
                            val city = addressItem.address.city
                            val province = addressItem.address.provname
                            val deliverydesc = addressItem.deliverydesc
                            val detail = addressItem.address.detail
                            val name = addressItem.name
                            val phone = addressItem.phone
                            val isdefault = addressItem.isdefault
                            val isdefaultText = if (isdefault == 1) "默认地址" else ""

                            val isSelectedStr = if (selectedAddressItem == addressItem) {
                                "已选择 "
                            } else {
                                ""
                            }

                            // 使用更安全的方式处理deliverydesc
                            val deliveryPrefix =
                                if (deliverydesc.isNotEmpty()) "$deliverydesc " else ""
                            val itemDisplayText =
                                "$isSelectedStr$area$detail $name,$phone $province$city $deliveryPrefix$isdefaultText"

                            DropdownMenuItem(
                                text = {
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = itemDisplayText,
                                            fontSize = 14.sp,
                                            maxLines = 1,
                                            modifier = Modifier
                                                .weight(1f)
                                                .horizontalScroll(rememberScrollState())
                                        )

                                        // 为已选择地址添加保存按钮
                                        if (selectedAddressItem == addressItem) {
                                            IconButton(
                                                onClick = { onSaveAddress(addressItem) },
                                                modifier = Modifier
                                                    .size(24.dp)
                                            ) {
                                                Icon(
                                                    painterResource(id = R.drawable.baseline_save_24),
                                                    contentDescription = "保存地址配置",
                                                    tint = Color(0xFF48454E),
                                                    modifier = Modifier.size(20.dp)
                                                )
                                            }
                                        }

                                        IconButton(
                                            onClick = { onDeleteAddress(addressItem) },
                                            modifier = Modifier.size(24.dp)
                                        ) {
                                            Icon(
                                                imageVector = Icons.Filled.Close,
                                                contentDescription = "删除地址",
                                                tint = Color(0xFF48454E),
                                                modifier = Modifier.size(20.dp)
                                            )
                                        }
                                    }
                                },
                                onClick = { onSelectAddress(addressItem) }
                            )
                        }
                    }
                }
            }

            Row(
                modifier = modifier
            ) {
                actions()
            }
        }
    }
}

/**
 * 通用确认对话框
 * @param title 对话框标题
 * @param message 对话框消息内容
 * @param onConfirm 点击确认按钮的回调
 * @param onDismiss 点击取消按钮或对话框外部的回调
 */
@Composable
fun ConfirmationDialog(
    title: String,
    message: String,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(title) },
        text = { Text(message) },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss,
            ) {
                Text("取消")
            }
        },
        containerColor = dialogContainerColor()
    )
}

/**
 * 添加商品弹窗
 * @param onDismiss 取消按钮或对话框外部的回调
 * @param onProductConfigSave 保存商品配置回调
 */
@Composable
fun AddProductDialog(
    productConfigStr: String,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit,
    onProductConfigSave: (String) -> Unit,
    onCopyProductConfig: (String) -> Unit,
    onPasteProductConfig: () -> Unit
) {
    val context = LocalContext.current
    var inputStr by remember(productConfigStr) { mutableStateOf(productConfigStr) }

    val isInputValid by remember(productConfigStr, inputStr) {
        derivedStateOf {
            inputStr.isNotEmpty() && inputStr.contains(";") && inputStr.contains(",")
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("添加商品") },
        containerColor = dialogContainerColor(),
        text = {
            Column {
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row(modifier = Modifier.horizontalScroll(rememberScrollState())) {
                        // 保存配置按钮
                        TokenActionButton(
                            icon = R.drawable.baseline_save_24,
                            text = "商品配置",
                            onClick = {
                                if (inputStr.isNotEmpty()) {
                                    onProductConfigSave(inputStr)
                                    Toast.makeText(context, "商品配置已保存", Toast.LENGTH_SHORT)
                                        .show()
                                } else {
                                    Toast.makeText(context, "请先输入商品信息", Toast.LENGTH_SHORT)
                                        .show()
                                }
                            }
                        )

                        // 复制按钮
                        TokenActionButton(
                            icon = R.drawable.baseline_copy_all_24,
                            text = "复制配置",
                            onClick = {
                                onCopyProductConfig(inputStr)
                            }
                        )

                        // 粘贴按钮
                        TokenActionButton(
                            icon = R.drawable.baseline_content_paste_24,
                            text = "粘贴配置",
                            onClick = {
                                onPasteProductConfig()
                            }
                        )
                    }
                }

                OutlinedTextField(
                    value = inputStr,
                    onValueChange = { inputStr = it },
                    singleLine = true,
                    modifier = Modifier
                        .fillMaxWidth(),
                    label = { Text("商品配置 ID,NUM,0,CODE;") },
                    shape = RoundedCornerShape(8.dp)
                )
            }
        },
        confirmButton = {
            Button(onClick = { onConfirm(inputStr) }, enabled = isInputValid) {
                Text("确定")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 代理选择对话框
 */
@Composable
fun ProxySelectDialog(
    onDismiss: () -> Unit,
    onProxySelected: (String) -> Unit
) {
    val proxyOptions =
        listOf("pinzan", "shanchen", "ipcola_global", "oxylabs_global")
    var selectedProxy by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("选择代理服务商") },
        containerColor = dialogContainerColor(),
        text = {
            Column {
                proxyOptions.forEach { proxy ->
                    ProxyConfigCard(
                        proxyType = proxy,
                        isSelected = proxy == selectedProxy,
                        onClick = { selectedProxy = proxy }
                    )
                }
            }
        },
        confirmButton = {
            Button(onClick = {
                onProxySelected(selectedProxy)
            }, enabled = selectedProxy.isNotEmpty()) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

@Composable
private fun ProxyConfigCard(
    proxyType: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) Color(0xCBF7DEF4) else CardContainerColor,
        ),
        modifier = Modifier
            .padding(vertical = 2.dp)
            .height(56.dp)
            .fillMaxWidth()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxSize()
                .clickable(onClick = onClick)
                .padding(horizontal = 16.dp)
        ) {
            Text(
                text = when (proxyType) {
                    "pinzan" -> "品赞 四川动态"
                    "shanchen" -> "闪臣 福建动态"
                    "ipcola_global" -> "IPCOLA 海外住宅动态"
                    "oxylabs_global" -> "Oxylabs 海外移动动态"
                    else -> "不使用代理"
                },
                fontSize = 16.sp
            )
        }
    }
}

/**
 * 范围确认对话框，用于删除指定范围的账号
 * @param title 对话框标题
 * @param message 对话框消息内容
 * @param onConfirmAll 点击清空全部按钮的回调
 * @param onConfirmRange 点击删除范围按钮的回调，传递起始和结束索引
 * @param onDismiss 点击取消按钮或对话框外部的回调
 * @param totalAccounts 总账号数量
 */
@Composable
fun RangeConfirmationDialog(
    title: String,
    message: String,
    onConfirmAll: () -> Unit,
    onConfirmRange: (Int, Int) -> Unit,
    onDismiss: () -> Unit,
    totalAccounts: Int
) {
    var startIndex by remember { mutableStateOf("1") }
    var endIndex by remember { mutableStateOf(totalAccounts.toString()) }

    val isRangeValid = remember(startIndex, endIndex, totalAccounts) {
        try {
            val start = startIndex.toInt()
            val end = endIndex.toInt()
            start in 1..totalAccounts && end in 1..totalAccounts && start <= end
        } catch (e: NumberFormatException) {
            false
        }
    }

    val rangeCount = remember(startIndex, endIndex, isRangeValid) {
        if (isRangeValid) {
            try {
                endIndex.toInt() - startIndex.toInt() + 1
            } catch (e: NumberFormatException) {
                0
            }
        } else {
            0
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(title) },
        text = {
            Column {
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = "账号范围 (1-$totalAccounts)",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OutlinedTextField(
                        value = startIndex,
                        onValueChange = { startIndex = it },
                        label = { Text("起始") },
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.weight(1f)
                    )

                    Text(
                        text = " - ",
                        modifier = Modifier.padding(horizontal = 8.dp),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface
                    )

                    OutlinedTextField(
                        value = endIndex,
                        onValueChange = { endIndex = it },
                        label = { Text("结束") },
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.weight(1f)
                    )
                }

                if (!isRangeValid) {
                    Text(
                        "请输入有效范围 (1-$totalAccounts)",
                        color = MaterialTheme.colorScheme.error,
                        fontSize = 12.sp,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }

                /*Button(
                    onClick = onConfirmAll,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error,
                        contentColor = Color.White
                    ),
                    shape = RoundedCornerShape(8.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("清空全部账号", fontWeight = FontWeight.Medium)
                }*/
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (startIndex.toInt() == 1 && endIndex.toInt() == totalAccounts) {
                        onConfirmAll()
                    } else {
                        onConfirmRange(startIndex.toInt(), endIndex.toInt())
                    }
                },
                enabled = isRangeValid,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error,
                    contentColor = Color.White,
                    disabledContainerColor = MaterialTheme.colorScheme.error.copy(alpha = 0.3f),
                    disabledContentColor = Color.White.copy(alpha = 0.6f)
                ),
            ) {
                val buttonText = if (isRangeValid && rangeCount > 0) {
                    "确认删除(${rangeCount}个)"
                } else {
                    "确认删除"
                }
                Text(buttonText)
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        },
        containerColor = dialogContainerColor()
    )
}

@Composable
fun NewPersonCouponShopParamsFastSetDialog(
    onDismiss: () -> Unit,
    newPersonCouponShopParams: String,
    preConfigShopParams: () -> List<JSONObject>,
    onSaveConfig: (String) -> Unit
) {
    val context = LocalContext.current
    val preConfigList = preConfigShopParams()

    // 如果newPersonCouponShopParams在preConfigList中，则选中该项，否则不选中
    var selectedIndex by remember {
        mutableStateOf(preConfigList.indexOfFirst {
            it.optString("newPersonPopupShopParams", "") == newPersonCouponShopParams
        })
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("新人券配置") },
        text = {
            Column {
                LazyColumn(
                    modifier = Modifier.heightIn(max = 300.dp)
                ) {
                    items(preConfigList.size) { index ->
                        val item = preConfigList[index]
                        val preName = item.optString("preName", "未命名")

                        Card(
                            colors = CardDefaults.cardColors(
                                containerColor = if (selectedIndex == index) Color(0xCBF7DEF4) else CardContainerColor
                            ),
                            modifier = Modifier
                                .padding(vertical = 2.dp)
                                .height(56.dp)
                                .fillMaxWidth()
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier
                                    .fillMaxSize()
                                    .clickable {
                                        selectedIndex = if (selectedIndex == index) {
                                            -1
                                        } else {
                                            index
                                        }
                                    }
                                    .padding(horizontal = 16.dp)
                            ) {
                                Text(
                                    text = preName,
                                    fontSize = 16.sp
                                )
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (selectedIndex >= 0 && selectedIndex < preConfigList.size) {
                        val selectedParams =
                            preConfigList[selectedIndex].optString("newPersonPopupShopParams", "")
                        if (selectedParams.isNotEmpty()) {
                            onSaveConfig(selectedParams)
                            Toast.makeText(context, "参数已保存", Toast.LENGTH_SHORT).show()
                        }
                    }
                    onDismiss()
                },
                enabled = selectedIndex >= 0
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        },
        containerColor = dialogContainerColor()
    )
}

/**
 * 添加账号对话框
 *
 * @param clipboardContent 剪贴板内容
 * @param onDismiss 取消回调
 * @param onConfirm 确认回调
 */
@Composable
fun AddAccountDialog(
    clipboardContent: String,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("添加账号") },
        text = {
            Column {
                Text("检测到剪贴板中包含账号信息，是否添加？")
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = clipboardContent,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        confirmButton = {
            Button(onClick = { onConfirm(clipboardContent) }) {
                Text("添加")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        },
        containerColor = dialogContainerColor()
    )
}

/**
 * 抢券倒计时指示器
 *
 * @param remainingTime 剩余时间（毫秒）
 */
@SuppressLint("DefaultLocale")
@Composable
private fun CouponGrabbingIndicator(remainingTime: Long) {

    // 使用状态来管理倒计时
    var currentRemainingTime by remember { mutableLongStateOf(remainingTime) }

    // 倒计时逻辑
    LaunchedEffect(remainingTime) {
        currentRemainingTime = remainingTime
        while (currentRemainingTime > 0) {
            delay(1000) // 每秒更新一次
            currentRemainingTime -= 1000
        }
    }

    Box(
        modifier = Modifier
            .padding(bottom = 16.dp)
            .background(
                color = CardContainerColor,
                shape = RoundedCornerShape(12.dp)
            )
            .border(
                width = 1.dp,
                color = Color(0xFF42A5F5).copy(alpha = 0.7f), // 蓝色边框
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 16.dp, vertical = 10.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 转换毫秒为可读时间格式
            val formattedTime = remember(currentRemainingTime) {
                if (currentRemainingTime <= 0) {
                    "正在抢券"
                } else {
                    val seconds = (currentRemainingTime / 1000) % 60
                    val minutes = (currentRemainingTime / (1000 * 60)) % 60
                    val hours = (currentRemainingTime / (1000 * 60 * 60))

                    if (hours > 0) {
                        String.format("%02d:%02d:%02d", hours, minutes, seconds)
                    } else {
                        String.format("%02d:%02d", minutes, seconds)
                    }
                }
            }

            // 闪烁效果
            val infiniteTransition =
                rememberInfiniteTransition(label = "coupon_animation")
            val alpha by infiniteTransition.animateFloat(
                initialValue = 0.4f,
                targetValue = 1.0f,
                animationSpec = infiniteRepeatable(
                    animation = tween(1000, easing = LinearEasing),
                    repeatMode = RepeatMode.Reverse
                ),
                label = "alpha"
            )

            Icon(
                painter = painterResource(id = R.drawable.outline_timer_off_24),
                contentDescription = null,
                tint = if (remainingTime <= 0)
                    Color(0xFF42A5F5).copy(alpha = alpha)
                else Color(0xFF42A5F5),
                modifier = Modifier
                    .size(20.dp)
            )

            Text(
                text = formattedTime,
                color = if (remainingTime <= 0)
                    MaterialTheme.colorScheme.primary.copy(alpha = alpha)
                else MaterialTheme.colorScheme.primary,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * 操作进度指示器
 * 显示当前批量操作的进度状态
 */
@Composable
private fun OperationProgressIndicator() {
    // 收集操作进度状态
    val progressState by OperationProgressUtils.progressFlow.collectAsState()
    val progressPercentage = OperationProgressUtils.getProgressPercentage()
    val successCount = progressState.successCount
    val failCount = progressState.failCount
    val totalFinishedCount = successCount + failCount
    val isComplete =
        progressPercentage >= 100 || totalFinishedCount == progressState.totalCount

    val operationProgressInfo =
        "${progressState.totalCount},${successCount},${failCount},${progressPercentage}"
    // 更新后台服务中的操作进度信息
    BackgroundService.updateRegistrationInfo(
        operationProgressInfo,
        "progress"
    )

    if (OperationProgressUtils.isOperationInProgress()) {
        Box(
            modifier = Modifier
                .background(
                    color = CardContainerColor,
                    shape = RoundedCornerShape(12.dp)
                )
                .border(
                    width = 1.dp,
                    color = Color(0xFFFFD700).copy(alpha = 0.5f),
                    shape = RoundedCornerShape(12.dp)
                )
                .padding(horizontal = 16.dp, vertical = 10.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                if (isComplete) {
                    IconButton(
                        onClick = {
                            OperationProgressUtils.completeProgress()
                        },
                        modifier = Modifier.size(20.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Done,
                            contentDescription = "关闭进度指示器",
                            tint = Color(0xFFF8BBD0),
                            modifier = Modifier.size(20.dp)
                        )
                    }
                } else {
                    // 旋转的进度指示器
                    val infiniteTransition =
                        rememberInfiniteTransition(label = "auto_animation")
                    val rotation by infiniteTransition.animateFloat(
                        initialValue = 0f,
                        targetValue = 360f,
                        animationSpec = infiniteRepeatable(
                            animation = tween(1500, easing = LinearEasing),
                            repeatMode = RepeatMode.Restart
                        ),
                        label = "rotation"
                    )

                    Icon(
                        painter = painterResource(id = R.drawable.outline_sync_24),
                        contentDescription = null,
                        tint = Color(0xFFFFD700),
                        modifier = Modifier
                            .size(20.dp)
                            .graphicsLayer { rotationZ = rotation }
                    )
                }

                Text(
                    text = "${totalFinishedCount}/${progressState.totalCount}  ${successCount},${failCount},$progressPercentage%",
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    fontSize = 12.sp
                )
            }
        }
    }
}

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun FastScrollBar(
    state: LazyListState,
    modifier: Modifier = Modifier,
    minThumbHeight: Dp = 40.dp,  // 增加默认最小高度
    thumbWidth: Dp = 8.dp,       // 滑块宽度参数
    trackWidth: Dp = 4.dp,       // 轨道宽度参数
    thumbColor: Color = MaterialTheme.colorScheme.primary.copy(alpha = 0.9f),  // 滑块颜色
    trackColor: Color = Color.Transparent, // 轨道颜色
    autoHideDelay: Long = 1500 // 自动隐藏延迟时间(毫秒)
) {
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current

    // 滑块动画状态
    var isThumbPressed by remember { mutableStateOf(false) }
    var isScrollBarVisible by remember { mutableStateOf(false) }

    // 使用 snapshotFlow 观察 layoutInfo 变化，避免频繁重组
    val layoutInfo by remember {
        derivedStateOf {
            state.layoutInfo
        }
    }

    // 检查是否需要显示滚动条
    val shouldShowScrollBar by remember {
        derivedStateOf {
            layoutInfo.totalItemsCount > layoutInfo.visibleItemsInfo.size
        }
    }

    // 如果不需要显示滚动条，直接返回
    if (!shouldShowScrollBar) {
        return
    }

    // 跟踪滚动状态变化
    val isScrollInProgress = state.isScrollInProgress

    // 当滚动开始时显示滚动条
    LaunchedEffect(isScrollInProgress) {
        if (isScrollInProgress) {
            isScrollBarVisible = true
        } else if (!isThumbPressed) {
            // 当滚动停止且滑块未被按下时，延迟隐藏滚动条
            delay(autoHideDelay)
            if (!isThumbPressed && !state.isScrollInProgress) {
                isScrollBarVisible = false
            }
        }
    }

    // 当滑块被按下时保持可见
    LaunchedEffect(isThumbPressed) {
        if (isThumbPressed) {
            isScrollBarVisible = true
        } else if (!state.isScrollInProgress) {
            // 当滑块释放且滚动已停止时，延迟隐藏
            delay(autoHideDelay)
            if (!isThumbPressed && !state.isScrollInProgress) {
                isScrollBarVisible = false
            }
        }
    }

    // 滚动条可见性动画
    val scrollBarAlpha by animateFloatAsState(
        targetValue = if (isScrollBarVisible) 1f else 0f,
        animationSpec = tween(durationMillis = 300),
        label = "scrollBarAlpha"
    )

    // 如果滚动条完全不可见，则不渲染
    if (scrollBarAlpha <= 0f) {
        return
    }

    val thumbAlpha by animateFloatAsState(
        targetValue = if (isThumbPressed) 1f else 0.7f,
        label = "thumbAlpha"
    )

    BoxWithConstraints(
        modifier = modifier
            .width(24.dp)
            .graphicsLayer { alpha = scrollBarAlpha }) {
        val containerHeight = constraints.maxHeight.toFloat()

        // 调试日志
        /*LaunchedEffect(Unit) {
            Log.d("FastScrollBar", "Container height: $containerHeight")
            Log.d("FastScrollBar", "Min thumb height: ${minThumbHeight.value}")
        }*/

        val onGesture: (Offset) -> Unit = { offset ->
            val targetFraction = (offset.y / containerHeight).coerceIn(0f, 1f)
            val targetItem = (state.layoutInfo.totalItemsCount * targetFraction).toInt()
            coroutineScope.launch {
                state.scrollToItem(targetItem)
            }
        }

        val gestureModifier = Modifier
            .pointerInput(Unit) {
                detectDragGestures(
                    onDragStart = {
                        isThumbPressed = true
                        isScrollBarVisible = true
                    },
                    onDragEnd = { isThumbPressed = false },
                    onDragCancel = { isThumbPressed = false }
                ) { change, _ ->
                    onGesture(change.position)
                    change.consume()
                }
            }
            .pointerInput(Unit) {
                detectTapGestures(
                    onPress = {
                        isThumbPressed = true
                        isScrollBarVisible = true
                        tryAwaitRelease()
                        isThumbPressed = false
                    },
                    onTap = { offset ->
                        isScrollBarVisible = true
                        onGesture(offset)
                    }
                )
            }

        Box(modifier = gestureModifier.fillMaxSize()) {
            // 计算滑块高度，确保不小于最小高度
            val minThumbHeightPx = with(LocalDensity.current) { minThumbHeight.toPx() }

            val thumbHeightPx by remember {
                derivedStateOf {
                    val visibleItemsCount = layoutInfo.visibleItemsInfo.size
                    val totalItemsCount = layoutInfo.totalItemsCount
                    val calculatedHeight =
                        containerHeight * (visibleItemsCount.toFloat() / totalItemsCount)
                    val finalHeight = calculatedHeight.coerceAtLeast(minThumbHeightPx)

                    // 调试日志
                    /*Log.d(
                        "FastScrollBar",
                        "Visible items: $visibleItemsCount, Total items: $totalItemsCount"
                    )
                    Log.d(
                        "FastScrollBar",
                        "Calculated height: $calculatedHeight, Final height: $finalHeight"
                    )*/

                    finalHeight
                }
            }

            // 调整滚动偏移量计算，考虑最小高度
            val thumbOffsetYPx by remember {
                derivedStateOf {
                    val visibleItemsCount = layoutInfo.visibleItemsInfo.size
                    val totalItemsCount = layoutInfo.totalItemsCount
                    if (totalItemsCount > visibleItemsCount) {
                        val scrollableItems = totalItemsCount - visibleItemsCount
                        val scrollablePixels = containerHeight - thumbHeightPx
                        val averageItemHeight = if (visibleItemsCount > 0) {
                            layoutInfo.visibleItemsInfo.sumOf { it.size } / visibleItemsCount.toFloat()
                        } else {
                            0f
                        }
                        val scrolledItems =
                            state.firstVisibleItemIndex + (state.firstVisibleItemScrollOffset / averageItemHeight)
                        val offset =
                            (scrollablePixels * (scrolledItems / scrollableItems)).coerceIn(
                                0f,
                                scrollablePixels
                            )

                        // 调试日志
                        // Log.d("FastScrollBar", "Offset calculation: $offset")

                        offset
                    } else {
                        0f
                    }
                }
            }

            val thumbHeight: Dp = with(LocalDensity.current) { thumbHeightPx.toDp() }
            val thumbOffsetY: Dp = with(LocalDensity.current) { thumbOffsetYPx.toDp() }

            // 轨道
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .width(trackWidth)
                    .background(
                        trackColor,
                        RoundedCornerShape(trackWidth / 2)
                    )
                    .align(Alignment.Center)
            )

            // 滑块
            Box(
                modifier = Modifier
                    .width(thumbWidth)
                    .height(thumbHeight)
                    .offset(y = thumbOffsetY)
                    .background(
                        thumbColor.copy(alpha = thumbAlpha),
                        RoundedCornerShape(thumbWidth / 2)
                    )
                    .align(Alignment.TopCenter)
                    // 添加阴影效果
                    .graphicsLayer {
                        if (isThumbPressed) {
                            shadowElevation = 4f
                        }
                    }
            )
        }
    }
}