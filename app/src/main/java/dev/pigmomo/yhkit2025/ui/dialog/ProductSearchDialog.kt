package dev.pigmomo.yhkit2025.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import dev.pigmomo.yhkit2025.api.model.search.SearchResult
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor

/**
 * 商品搜索对话框组件
 *
 * @param searchKeyword 搜索关键词
 * @param searchResults 搜索结果列表
 * @param isLoading 是否正在加载
 * @param errorMessage 错误信息
 * @param hasNext 是否有下一页 (1表示有，0表示没有)
 * @param totalPage 总页数
 * @param currentPage 当前页码
 * @param isLoadingMore 是否正在加载更多
 * @param onDismiss 对话框关闭回调
 * @param onSearchKeywordChange 搜索关键词变化回调
 * @param onSearch 搜索回调
 * @param onProductClick 商品点击回调
 * @param onLoadMore 加载更多回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductSearchDialog(
    searchKeyword: String,
    searchResults: List<SearchResult>,
    isLoading: Boolean,
    errorMessage: String?,
    hasNext: Int = 0,
    totalPage: Int = 0,
    currentPage: Int = 1,
    isLoadingMore: Boolean = false,
    onDismiss: () -> Unit = {},
    onSearchKeywordChange: (String) -> Unit = {},
    onSearch: (String) -> Unit = {},
    onProductClick: (SearchResult) -> Unit = {},
    onLoadMore: () -> Unit = {}
) {
    val keyboardController = LocalSoftwareKeyboardController.current

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("商品搜索") },
        containerColor = dialogContainerColor(),
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(500.dp)
            ) {
                // 搜索输入框
                OutlinedTextField(
                    value = searchKeyword,
                    onValueChange = onSearchKeywordChange,
                    label = { Text("输入商品关键词") },
                    placeholder = { Text("请输入要搜索的商品名称") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Search,
                            contentDescription = "搜索"
                        )
                    },
                    trailingIcon = {
                        if (isLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                strokeWidth = 2.dp
                            )
                        }
                    },
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Search
                    ),
                    keyboardActions = KeyboardActions(
                        onSearch = {
                            keyboardController?.hide()
                            onSearch(searchKeyword)
                        }
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp)
                )

                // 搜索按钮
                Button(
                    onClick = {
                        keyboardController?.hide()
                        onSearch(searchKeyword)
                    },
                    enabled = !isLoading && searchKeyword.isNotBlank(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp)
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp,
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    Text(if (isLoading) "搜索中..." else "搜索")
                }

                // 错误信息显示
                errorMessage?.let { error ->
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp)
                    ) {
                        Text(
                            text = error,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            modifier = Modifier.padding(12.dp)
                        )
                    }
                }

                // 搜索结果列表
                if (searchResults.isNotEmpty()) {
                    // 显示搜索结果统计和分页信息
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "搜索结果 (${searchResults.size})",
                            style = MaterialTheme.typography.titleSmall
                        )

                        if (totalPage > 0) {
                            Text(
                                text = "第${currentPage}页/共${totalPage}页",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }

                    LazyColumn(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(searchResults) { result ->
                            ProductSearchItem(
                                searchResult = result,
                                onClick = { onProductClick(result) }
                            )
                        }

                        // 加载更多项
                        if (hasNext == 1) {
                            item {
                                LoadMoreItem(
                                    isLoading = isLoadingMore,
                                    onLoadMore = onLoadMore
                                )
                            }
                        } else if (searchResults.isNotEmpty() && totalPage > 0) {
                            item {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "已加载全部结果",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        }
                    }
                } else if (!isLoading && searchKeyword.isNotBlank() && errorMessage == null) {
                    // 空状态显示
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "暂无搜索结果",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = "请尝试其他关键词",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.padding(top = 4.dp)
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("关闭")
            }
        },
        dismissButton = null
    )
}

/**
 * 商品搜索结果项组件
 */
@Composable
private fun ProductSearchItem(
    searchResult: SearchResult,
    onClick: () -> Unit
) {
    val skuBlock = searchResult.skuBlock
    if (skuBlock == null) return

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 商品图片
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(skuBlock.cover?.imageUrl ?: skuBlock.cover?.url)
                    .crossfade(true)
                    .build(),
                contentDescription = skuBlock.title,
                modifier = Modifier
                    .size(60.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(Color.White),
                contentScale = ContentScale.Crop
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 商品信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 商品标题
                Text(
                    text = skuBlock.title ?: "未知商品",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )

                // 商品副标题
                skuBlock.subTitle?.let { subTitle ->
                    Text(
                        text = subTitle,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }

                // 价格信息
                skuBlock.price?.let { price ->
                    val priceText = when {
                        price.price != null -> "¥${price.price}"
                        price.value != null -> "¥${String.format("%.2f", price.value / 100.0)}"
                        else -> "价格未知"
                    }
                    Text(
                        text = priceText,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }

                // 库存状态
                val stockText = when (skuBlock.inStock) {
                    1 -> "有库存"
                    0 -> "缺货"
                    else -> "未知"
                }
                val stockColor = when (skuBlock.inStock) {
                    1 -> Color(0xFF4CAF50)
                    0 -> Color(0xFFF44336)
                    else -> MaterialTheme.colorScheme.onSurfaceVariant
                }

                Text(
                    text = stockText,
                    style = MaterialTheme.typography.bodySmall,
                    color = stockColor,
                    modifier = Modifier.padding(top = 2.dp)
                )
            }
        }
    }
}

/**
 * 加载更多组件
 */
@Composable
private fun LoadMoreItem(
    isLoading: Boolean,
    onLoadMore: () -> Unit
) {
    LaunchedEffect(Unit) {
        if (!isLoading) {
            onLoadMore()
        }
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        if (isLoading) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp
                )
                Text(
                    text = "加载中...",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        } else {
            Text(
                text = "正在加载更多...",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
