package dev.pigmomo.yhkit2025.api.utils

import android.util.Log
import com.google.gson.*
import com.google.gson.reflect.TypeToken
import dev.pigmomo.yhkit2025.api.model.search.*
import java.lang.reflect.Type

/**
 * 搜索响应解析工具类
 * 提供健壮的JSON解析，处理API响应中的数据类型不匹配问题
 */
object SearchResponseParser {
    
    private val tag = "SearchResponseParser"
    
    /**
     * 安全解析搜索响应
     */
    fun parseSearchResponse(jsonString: String): SearchResponse? {
        return try {
            val gson = createGson()
            gson.fromJson(jsonString, SearchResponse::class.java)
        } catch (e: Exception) {
            Log.e(tag, "Failed to parse search response", e)
            null
        }
    }
    
    /**
     * 创建自定义的Gson实例，包含自定义的反序列化器
     */
    private fun createGson(): Gson {
        return GsonBuilder()
            .setLenient()
            .registerTypeAdapter(Tag::class.java, TagDeserializer())
            .registerTypeAdapter(Cover::class.java, CoverDeserializer())
            .registerTypeAdapter(Price::class.java, PriceDeserializer())
            .create()
    }
    
    /**
     * Tag的自定义反序列化器
     * 处理commonTags字段可能是字符串数组或对象数组的情况
     */
    private class TagDeserializer : JsonDeserializer<Tag> {
        override fun deserialize(
            json: JsonElement,
            typeOfT: Type,
            context: JsonDeserializationContext
        ): Tag {
            val jsonObject = json.asJsonObject
            val commonTagsElement = jsonObject.get("commonTags")
            
            val commonTags = when {
                commonTagsElement == null || commonTagsElement.isJsonNull -> null
                commonTagsElement.isJsonArray -> {
                    try {
                        val array = commonTagsElement.asJsonArray
                        array.mapNotNull { element ->
                            when {
                                element.isJsonPrimitive -> {
                                    // 如果是字符串，创建简单的CommonTag对象
                                    CommonTag(text = element.asString, type = null, uitype = null, sort = null)
                                }
                                element.isJsonObject -> {
                                    // 如果是对象，正常解析
                                    context.deserialize<CommonTag>(element, CommonTag::class.java)
                                }
                                else -> null
                            }
                        }
                    } catch (e: Exception) {
                        Log.w(tag, "Failed to parse commonTags array", e)
                        emptyList()
                    }
                }
                else -> emptyList()
            }
            
            return Tag(commonTags = commonTags)
        }
    }
    
    /**
     * Cover的自定义反序列化器
     * 处理图片URL字段名称可能不同的情况
     */
    private class CoverDeserializer : JsonDeserializer<Cover> {
        override fun deserialize(
            json: JsonElement,
            typeOfT: Type,
            context: JsonDeserializationContext
        ): Cover {
            val jsonObject = json.asJsonObject
            
            val imageUrl = jsonObject.get("imageUrl")?.takeIf { !it.isJsonNull }?.asString
            val url = jsonObject.get("url")?.takeIf { !it.isJsonNull }?.asString
            
            return Cover(
                imageUrl = imageUrl,
                url = url
            )
        }
    }
    
    /**
     * Price的自定义反序列化器
     * 处理价格字段可能是字符串或数字的情况
     */
    private class PriceDeserializer : JsonDeserializer<Price> {
        override fun deserialize(
            json: JsonElement,
            typeOfT: Type,
            context: JsonDeserializationContext
        ): Price {
            val jsonObject = json.asJsonObject
            
            val price = jsonObject.get("price")?.takeIf { !it.isJsonNull }?.let { element ->
                when {
                    element.isJsonPrimitive && element.asJsonPrimitive.isString -> element.asString
                    element.isJsonPrimitive && element.asJsonPrimitive.isNumber -> element.asString
                    else -> null
                }
            }
            
            val value = jsonObject.get("value")?.takeIf { !it.isJsonNull }?.let { element ->
                try {
                    when {
                        element.isJsonPrimitive && element.asJsonPrimitive.isNumber -> element.asInt
                        element.isJsonPrimitive && element.asJsonPrimitive.isString -> element.asString.toIntOrNull()
                        else -> null
                    }
                } catch (e: Exception) {
                    null
                }
            }
            
            val market = jsonObject.get("market")?.takeIf { !it.isJsonNull }?.let { element ->
                try {
                    when {
                        element.isJsonPrimitive && element.asJsonPrimitive.isNumber -> element.asInt
                        element.isJsonPrimitive && element.asJsonPrimitive.isString -> element.asString.toIntOrNull()
                        else -> null
                    }
                } catch (e: Exception) {
                    null
                }
            }
            
            return Price(
                price = price,
                value = value,
                market = market
            )
        }
    }
}
